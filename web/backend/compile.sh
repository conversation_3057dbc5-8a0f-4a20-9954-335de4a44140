#!/bin/bash

# Web Backend Nuitka本地编译脚本
# 用于在本地主机编译Django Web服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 设置编译缓存环境
setup_cache_environment() {
    print_step "设置编译缓存环境..."

    # 设置缓存目录
    export CCACHE_DIR="/ssd/ccache"

    # 创建缓存目录
    mkdir -p "$CCACHE_DIR" # Quoted CCACHE_DIR

    # 检查是否安装了ccache和gcc
    if command -v ccache >/dev/null 2>&1; then
        print_success "ccache 已安装"
        ccache -s 2>/dev/null || print_info "ccache 统计信息不可用"

        # 检查gcc是否存在
        if command -v gcc >/dev/null 2>&1; then
            print_success "gcc 编译器已安装"

            # 创建ccache符号链接目录（推荐方式）
            mkdir -p /tmp/ccache-links
            ln -sf "$(which ccache)" /tmp/ccache-links/gcc
            ln -sf "$(which ccache)" /tmp/ccache-links/g++
            ln -sf "$(which ccache)" /tmp/ccache-links/cc
            ln -sf "$(which ccache)" /tmp/ccache-links/c++

            # 将ccache链接目录添加到PATH前面
            export PATH="/tmp/ccache-links:$PATH"

            print_success "ccache 符号链接已设置"
        else
            print_error "gcc 编译器未安装，请安装: sudo apt-get install build-essential"
            exit 1
        fi
    else
        print_warning "ccache 未安装，将使用普通编译（较慢）"
        print_info "建议安装以加速编译: sudo apt-get install ccache"
    fi

    print_success "缓存环境设置完成"
}

# 检查环境
check_environment() {
    print_step "检查编译环境..."

    # 检查是否在backend目录
    if [[ ! -f "main.py" ]]; then
        print_error "请在web/backend目录下运行此脚本"
        exit 1
    fi

    # 检查虚拟环境
    # Allow skipping .venv if NUITKA_PYTHON_EXE is set for system python usage
    if [[ -z "$NUITKA_PYTHON_EXE" && ! -d ".venv" ]]; then
        print_error "虚拟环境 .venv 不存在且 NUITKA_PYTHON_EXE 未设置"
        exit 1
    fi
    
    PYTHON_EXE=${NUITKA_PYTHON_EXE:-.venv/bin/python}

    # 检查Nuitka
    if ! "$PYTHON_EXE" -c "import nuitka" 2>/dev/null; then
        print_error "Nuitka未安装，请先安装: uv add nuitka (或在指定Python环境中安装)"
        exit 1
    fi

    print_success "环境检查通过"
}

# 清理旧文件
cleanup_old_files() {
    print_step "清理旧的编译文件..."
    
    rm -rf main.build/ # Nuitka uses script name for build folder by default
    rm -rf main.dist/
    rm -f backend_server # Old onefile name
    rm -f *.so # In case of --module mode remnants
    
    print_success "清理完成"
}

# 编译函数
compile_backend() {
    print_step "开始Nuitka编译..."
    print_info "这可能需要几分钟时间，请耐心等待..."

    PYTHON_EXE=${NUITKA_PYTHON_EXE:-.venv/bin/python}


    "$PYTHON_EXE" -m nuitka \
    --standalone \
    --assume-yes-for-downloads \
    --output-dir=main.dist \
    --include-data-files=conf/env.py=conf/env.py \
    --include-data-files=conf/env.example.py=conf/env.example.py \
    --include-data-dir=static=static \
    --include-data-dir=templates=templates \
    --include-data-dir=media=media \
    --include-package=dvadmin \
    --include-package=application \
    --include-package=websocket \
    --include-package=utils \
    --include-package=plugins \
    --nofollow-import-to=django \
    --nofollow-import-to=rest_framework \
    --nofollow-import-to=corsheaders \
    --nofollow-import-to=captcha \
    --nofollow-import-to=channels \
    --nofollow-import-to=uvicorn \
    --nofollow-import-to=gunicorn \
    --nofollow-import-to=openpyxl \
    --nofollow-import-to=requests \
    --nofollow-import-to=six \
    --nofollow-import-to=whitenoise \
    --nofollow-import-to=gevent \
    --nofollow-import-to=celery \
    --nofollow-import-to=pandas \
    --nofollow-import-to=mysqlclient \
    --nofollow-import-to=pkg_resources \
    --nofollow-import-to=setuptools \
    --nofollow-import-to=distutils \
    --nofollow-import-to=pip \
    --nofollow-import-to=pytest \
    --nofollow-import-to=unittest \
    --nofollow-import-to=test \
    --nofollow-import-to=tests \
    --enable-plugin=anti-bloat \
    --remove-output \
    --no-pyi-file \
    --lto=no \
    --jobs=$(nproc) \
    main.py

    if [[ $? -eq 0 ]]; then
        print_success "编译成功！输出在 main.dist/"
        # Rename the executable inside main.dist for consistency if needed
        if [[ -f "main.dist/main.bin" ]]; then # Nuitka default for non-onefile
            mv "main.dist/main.bin" "main.dist/backend_server"
            print_info "重命名 main.dist/main.bin 为 main.dist/backend_server"
        elif [[ -f "main.dist/main" ]]; then
             mv "main.dist/main" "main.dist/backend_server"
             print_info "重命名 main.dist/main 为 main.dist/backend_server"
        fi
        return 0
    else
        print_error "编译失败"
        return 1
    fi
}

# 验证编译结果
verify_compilation() {
    print_step "验证编译结果..."
    
    if [[ ! -f "main.dist/backend_server" ]]; then
        print_error "可执行文件 main.dist/backend_server 不存在"
        return 1
    fi
    
    # 检查文件权限
    chmod +x "main.dist/backend_server"
    
    # 显示文件信息
    print_info "可执行文件信息:"
    ls -lh "main.dist/backend_server"
    
    # 测试健康检查
    # This test might not work easily without the full environment from Docker.
    # print_info "测试健康检查功能..."
    # if timeout 10 ./main.dist/backend_server --help >/dev/null 2>&1; then
    #     print_success "可执行文件运行正常 (通过 --help)"
    # else
    #     print_warning "可执行文件 --help 测试失败或超时"
    # fi
    
    print_success "编译验证完成"
}

# 创建部署包
create_deployment_package() {
    print_step "创建部署包..."
    
    local DEPLOY_DIR="backend_deploy_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$DEPLOY_DIR"
    
    # 复制编译后的应用 (the .dist directory)
    print_info "复制 main.dist 到 $DEPLOY_DIR/app_dist"
    cp -r main.dist "$DEPLOY_DIR/app_dist"
    
    # 复制配置文件
    cp -r conf "$DEPLOY_DIR/"
    
    # 复制静态文件 (Nuitka --include-data-dir should handle these being inside app_dist)
    # However, if Dockerfile needs them separately at root of /backend, copy them.
    # For now, assume Nuitka bundles them correctly and they are accessed relative to the exe.
    # If not, uncomment and adapt:
    # cp -r static "$DEPLOY_DIR/"
    # cp -r templates "$DEPLOY_DIR/"
    
    # 创建媒体文件目录 (if your app writes here at runtime)
    mkdir -p "$DEPLOY_DIR/media"
    
    # 创建日志目录
    mkdir -p "$DEPLOY_DIR/logs"
    
    # 创建启动脚本
    cat > "$DEPLOY_DIR/start.sh" << EOF
#!/bin/bash
# Web Backend服务器启动脚本

# 检查配置文件
if [[ ! -f "conf/env.py" ]]; then
    echo "错误: 配置文件 conf/env.py 不存在"
    exit 1
fi

# 启动服务器
echo "启动Web Backend服务器..."
# Execute the program from its location within app_dist
./app_dist/backend_server
EOF
    
    chmod +x "$DEPLOY_DIR/start.sh"
    
    # 创建健康检查脚本
    cat > "$DEPLOY_DIR/health_check.sh" << 'EOF'
#!/bin/bash
# Web Backend服务器健康检查脚本

# 检查HTTP响应
# Adjust port if necessary from conf/env.py or Gunicorn/Uvicorn settings
HEALTH_CHECK_URL="${HEALTH_CHECK_URL:-http://localhost:8000/health/}" 
if curl -f -s "$HEALTH_CHECK_URL" >/dev/null 2>&1; then
    echo "Web Backend服务器健康检查通过"
    exit 0
else
    echo "Web Backend服务器健康检查失败 ($HEALTH_CHECK_URL)"
    exit 1
fi
EOF
    
    chmod +x "$DEPLOY_DIR/health_check.sh"
    
    # 创建README
    cat > "$DEPLOY_DIR/README.md" << EOF
# Web Backend服务器部署包

## 文件说明
- \`app_dist/\`: 编译后的应用目录 (包含 \`backend_server\` 可执行文件及其依赖)
- \`conf/\`: 配置文件目录
- \`media/\`: 媒体文件目录 (运行时生成)
- \`logs/\`: 日志文件目录 (运行时生成)
- \`start.sh\`: 启动脚本
- \`health_check.sh\`: 健康检查脚本

## 部署步骤
1. 将整个目录复制到目标服务器或构建进Docker镜像。
2. 修改 \`conf/env.py\` 配置文件。
3. 确保 \`start.sh\` 和 \`app_dist/backend_server\` 有执行权限。
4. 运行 \`./start.sh\` 启动服务。

## 健康检查
运行 \`./health_check.sh\` 检查服务状态。
EOF
    
    print_success "部署包已创建: $DEPLOY_DIR"
    print_info "部署包大小: $(du -sh "$DEPLOY_DIR" | cut -f1)" # Quoted
}

# 主函数
main() {
    print_info "🚀 Web Backend本地编译脚本启动"

    check_environment
    setup_cache_environment # Enable ccache for faster compilation
    cleanup_old_files

    if compile_backend; then
        verify_compilation
        create_deployment_package
        print_success "🎉 编译完成！部署包已准备就绪"
        print_info "可以将生成的部署包复制到虚拟机进行部署或用于构建Docker镜像"
    else
        print_error "❌ 编译失败"
        exit 1
    fi
}

# 执行主函数
main "$@"