FROM ubuntu:24.04

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

# 配置apt代理
RUN echo 'Acquire::http::Proxy "http://**********:10808/";' > /etc/apt/apt.conf.d/proxy

# 安装基础包
RUN apt-get update && apt-get install -y --no-install-recommends \
    tzdata \
    ca-certificates \
    locales \
    curl \
    python3 \
    python3-pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装可用的Python依赖包
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3-django \
    python3-djangorestframework \
    python3-requests \
    python3-six \
    python3-openpyxl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 配置pip使用清华源
RUN pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 通过pip安装其他依赖 (使用--break-system-packages绕过PEP 668限制)
RUN pip3 install --no-cache-dir --break-system-packages \
    django-cors-headers \
    channels \
    uvicorn \
    gunicorn \
    gevent \
    celery \
    pandas \
    mysqlclient \
    django-simple-captcha \
    whitenoise \
    pypinyin

# 设置时区
RUN locale-gen en_US.UTF-8
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /backend

# 复制部署目录
COPY backend_deploy_20250609_213252/ /backend/

# 设置权限
RUN chmod +x /backend/start.sh /backend/health_check.sh
RUN find /backend/app_dist -name "backend_server" -exec chmod +x {} \;

# 创建目录
RUN mkdir -p /backend/logs && chmod 755 /backend/logs
RUN mkdir -p /backend/media && chmod 755 /backend/media

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /backend/health_check.sh

ENTRYPOINT ["/backend/start.sh"]
