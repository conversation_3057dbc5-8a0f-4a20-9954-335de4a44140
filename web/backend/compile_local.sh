#!/bin/bash

# Web Backend Nuitka本地编译脚本
# 用于在本地主机编译Django Web服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 设置编译缓存环境
setup_cache_environment() {
    print_step "设置编译缓存环境..."

    # 设置缓存目录
    export CCACHE_DIR="/ssd/ccache"

    # 创建缓存目录
    mkdir -p /ssd/ccache

    # 检查是否安装了ccache和gcc
    if command -v ccache >/dev/null 2>&1; then
        print_success "ccache 已安装"
        ccache -s 2>/dev/null || print_info "ccache 统计信息不可用"

        # 检查gcc是否存在
        if command -v gcc >/dev/null 2>&1; then
            print_success "gcc 编译器已安装"

            # 创建ccache符号链接目录（推荐方式）
            mkdir -p /tmp/ccache-links
            ln -sf $(which ccache) /tmp/ccache-links/gcc
            ln -sf $(which ccache) /tmp/ccache-links/g++

            # 将ccache链接目录添加到PATH前面
            export PATH="/tmp/ccache-links:$PATH"

            print_success "ccache 符号链接已设置"
        else
            print_error "gcc 编译器未安装，请安装: sudo apt-get install build-essential"
            exit 1
        fi
    else
        print_warning "ccache 未安装，将使用普通编译（较慢）"
        print_info "建议安装以加速编译: sudo apt-get install ccache"
    fi

    print_success "缓存环境设置完成"
}

# 检查环境
check_environment() {
    print_step "检查编译环境..."

    # 检查是否在backend目录
    if [[ ! -f "main.py" ]]; then
        print_error "请在web/backend目录下运行此脚本"
        exit 1
    fi

    # 检查虚拟环境
    if [[ ! -d ".venv" ]]; then
        print_error "虚拟环境 .venv 不存在"
        exit 1
    fi

    # 检查Nuitka
    if ! .venv/bin/python -c "import nuitka" 2>/dev/null; then
        print_error "Nuitka未安装，请先安装: uv add nuitka"
        exit 1
    fi

    print_success "环境检查通过"
}

# 清理旧文件
cleanup_old_files() {
    print_step "清理旧的编译文件..."
    
    rm -rf backend_server.build/
    rm -rf backend_server.dist/
    rm -rf backend_server.onefile-build/
    rm -f backend_server
    rm -f *.so
    
    print_success "清理完成"
}

# 编译函数
compile_backend() {
    print_step "开始Nuitka编译..."
    print_info "这可能需要几分钟时间，请耐心等待..."

    # 使用虚拟环境中的python和nuitka
    .venv/bin/python -m nuitka \
        --standalone \
        --onefile \
        --assume-yes-for-downloads \
        --cache-dir=/ssd/ccache \
        --output-filename=backend_server \
        --include-data-dir=conf=conf \
        --include-data-dir=static=static \
        --include-data-dir=templates=templates \
        --include-data-dir=media=media \
        --include-package=dvladmin \
        --include-package=application \
        --include-package=websocket \
        --include-package=utils \
        --include-package=plugins \
        --include-package=django \
        --include-package=rest_framework \
        --include-package=corsheaders \
        --include-package=captcha \
        --include-package=channels \
        --include-package=uvicorn \
        --include-package=gunicorn \
        --include-package=mysqlclient \
        --include-package=openpyxl \
        --include-package=requests \
        --include-package=pypinyin \
        --include-package=six \
        --include-package=whitenoise \
        --include-package=gevent \
        --include-package=celery \
        --include-package=pandas \
        --enable-plugin=anti-bloat \
        --remove-output \
        --no-pyi-file \
        --lto=no \
        --jobs=$(nproc) \
        --nofollow-import-to=pytest,unittest,test,tests \
        --nofollow-import-to=setuptools,distutils,pip \
        --nofollow-import-to=psycopg2 \
        main.py

    if [[ $? -eq 0 ]]; then
        print_success "编译成功！"
        return 0
    else
        print_error "编译失败"
        return 1
    fi
}

# 验证编译结果
verify_compilation() {
    print_step "验证编译结果..."
    
    if [[ ! -f "backend_server" ]]; then
        print_error "可执行文件 backend_server 不存在"
        return 1
    fi
    
    # 检查文件权限
    chmod +x backend_server
    
    # 显示文件信息
    print_info "可执行文件信息:"
    ls -lh backend_server
    
    # 测试健康检查
    print_info "测试健康检查功能..."
    if timeout 10 ./backend_server --help >/dev/null 2>&1; then
        print_success "可执行文件运行正常"
    else
        print_warning "健康检查超时，但这在没有完整配置时是正常的"
    fi
    
    print_success "编译验证完成"
}

# 创建部署包
create_deployment_package() {
    print_step "创建部署包..."
    
    local DEPLOY_DIR="backend_deploy_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$DEPLOY_DIR"
    
    # 复制可执行文件
    cp backend_server "$DEPLOY_DIR/"
    
    # 复制配置文件
    cp -r conf "$DEPLOY_DIR/"
    
    # 复制静态文件
    cp -r static "$DEPLOY_DIR/"
    
    # 复制模板文件
    cp -r templates "$DEPLOY_DIR/"
    
    # 创建媒体文件目录
    mkdir -p "$DEPLOY_DIR/media"
    
    # 创建日志目录
    mkdir -p "$DEPLOY_DIR/logs"
    
    # 创建启动脚本
    cat > "$DEPLOY_DIR/start.sh" << 'EOF'
#!/bin/bash
# Web Backend服务器启动脚本

# 检查配置文件
if [[ ! -f "conf/env.py" ]]; then
    echo "错误: 配置文件 conf/env.py 不存在"
    exit 1
fi

# 启动服务器
echo "启动Web Backend服务器..."
./backend_server
EOF
    
    chmod +x "$DEPLOY_DIR/start.sh"
    
    # 创建健康检查脚本
    cat > "$DEPLOY_DIR/health_check.sh" << 'EOF'
#!/bin/bash
# Web Backend服务器健康检查脚本

# 检查进程是否运行
if pgrep -f "backend_server" > /dev/null; then
    echo "Web Backend服务器正在运行"
    exit 0
else
    echo "Web Backend服务器未运行"
    exit 1
fi
EOF
    
    chmod +x "$DEPLOY_DIR/health_check.sh"
    
    # 创建README
    cat > "$DEPLOY_DIR/README.md" << 'EOF'
# Web Backend服务器部署包

## 文件说明
- `backend_server`: 编译后的可执行文件
- `conf/`: 配置文件目录
- `static/`: 静态文件目录
- `templates/`: 模板文件目录
- `media/`: 媒体文件目录
- `logs/`: 日志文件目录
- `start.sh`: 启动脚本
- `health_check.sh`: 健康检查脚本

## 部署步骤
1. 将整个目录复制到目标服务器
2. 修改 `conf/env.py` 配置文件
3. 运行 `./start.sh` 启动服务

## 健康检查
运行 `./health_check.sh` 检查服务状态

## 端口说明
- 8000: Web Backend API端口
EOF
    
    print_success "部署包已创建: $DEPLOY_DIR"
    print_info "部署包大小: $(du -sh $DEPLOY_DIR | cut -f1)"
}

# 主函数
main() {
    print_info "🚀 Web Backend本地编译脚本启动"
    
    check_environment
    cleanup_old_files
    
    if compile_backend; then
        verify_compilation
        create_deployment_package
        print_success "🎉 编译完成！可执行文件已准备就绪"
        print_info "可以将生成的部署包复制到虚拟机进行部署"
    else
        print_error "❌ 编译失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
