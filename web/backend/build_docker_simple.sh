#!/bin/bash

# 简化的Docker构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_info "🚀 简化Docker构建脚本启动"

# 查找部署目录
print_step "查找部署目录..."
DEPLOY_DIR=$(ls -1d backend_deploy_* 2>/dev/null | sort -r | head -n1)

if [[ -z "$DEPLOY_DIR" ]]; then
    print_error "未找到 backend_deploy_* 目录"
    exit 1
fi

print_info "找到部署目录: $DEPLOY_DIR"

# 检查必要文件
print_step "检查必要文件..."
REQUIRED_FILES=("app_dist" "start.sh" "health_check.sh" "conf")
for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -e "$DEPLOY_DIR/$file" ]]; then
        print_error "必需文件 '$file' 在 $DEPLOY_DIR 中不存在"
        exit 1
    fi
done

print_success "文件检查通过"

# 创建Dockerfile
print_step "创建Dockerfile..."
cat > Dockerfile.simple << EOF
FROM ubuntu:24.04

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

# 配置apt代理
RUN echo 'Acquire::http::Proxy "http://**********:10808/";' > /etc/apt/apt.conf.d/proxy

# 安装基础包
RUN apt-get update && apt-get install -y --no-install-recommends \\
    tzdata \\
    ca-certificates \\
    locales \\
    curl \\
    python3 \\
    python3-pip \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/*

# 安装可用的Python依赖包
RUN apt-get update && apt-get install -y --no-install-recommends \\
    python3-django \\
    python3-djangorestframework \\
    python3-requests \\
    python3-six \\
    python3-openpyxl \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/*

# 配置pip使用清华源
RUN pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 通过pip安装其他依赖 (使用--break-system-packages绕过PEP 668限制)
RUN pip3 install --no-cache-dir --break-system-packages \\
    django-cors-headers \\
    channels \\
    uvicorn \\
    gunicorn \\
    gevent \\
    celery \\
    pandas \\
    mysqlclient \\
    django-simple-captcha \\
    whitenoise \\
    pypinyin

# 设置时区
RUN locale-gen en_US.UTF-8
RUN ln -snf /usr/share/zoneinfo/\$TZ /etc/localtime && echo \$TZ > /etc/timezone

WORKDIR /backend

# 复制部署目录
COPY $DEPLOY_DIR/ /backend/

# 设置权限
RUN chmod +x /backend/start.sh /backend/health_check.sh
RUN find /backend/app_dist -name "backend_server" -exec chmod +x {} \\;

# 创建目录
RUN mkdir -p /backend/logs && chmod 755 /backend/logs
RUN mkdir -p /backend/media && chmod 755 /backend/media

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \\
    CMD /backend/health_check.sh

ENTRYPOINT ["/backend/start.sh"]
EOF

print_success "Dockerfile已创建"

# 构建镜像
print_step "构建Docker镜像..."
IMAGE_NAME="ynyb-backend-simple"
IMAGE_TAG="latest"

print_info "构建镜像: $IMAGE_NAME:$IMAGE_TAG"

if docker build -f Dockerfile.simple -t "$IMAGE_NAME:$IMAGE_TAG" .; then
    print_success "Docker镜像构建成功: $IMAGE_NAME:$IMAGE_TAG"
    
    # 显示镜像信息
    print_info "镜像信息:"
    docker images "$IMAGE_NAME:$IMAGE_TAG"
    
else
    print_error "Docker镜像构建失败"
    exit 1
fi

print_success "🎉 构建完成！"
print_info "运行命令: docker run -d --name ynyb-backend -p 8000:8000 $IMAGE_NAME:$IMAGE_TAG"
