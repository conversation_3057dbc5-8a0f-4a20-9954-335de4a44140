[project]
name = "vpdn-management"
version = "0.1.0"
description = "VPDN Management System"
# It's good practice to specify the Python version
requires-python = ">=3.12" # Or ">=3.11" if that's your minimum, adjust as needed

# You should eventually move all dependencies from requirements.txt here
# For now, just keeping what you have.
dependencies = [
    "niutka",
    "requests==2.32.3",
    # Add other direct dependencies here.
    # For example, Django, djangorestframework, etc., from your requirements.txt
    # "django~=4.2", # Example
    # "djangorestframework~=3.14", # Example
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
# List your top-level Python package directories here.
# Hatch will automatically find .py files and sub-packages within these.
packages = [
    "application",
    "dvadmin",
    "plugins",
    "conf" # Include this if you import from it, e.g., `from conf import env`
]

# If you have scripts that should be installed (like manage.py, but that's usually not "installed")
# you would define them under [project.scripts]
# e.g.
# [project.scripts]
# my-script = "application.cli:main"

# If you have top-level .py files in 'backend/' that are meant to be modules
# (e.g., if you would `import main` or `import gunicorn_conf` from other code),
# and they are not part of the packages listed above, you might need to add:
# include = ["main.py", "gunicorn_conf.py"]
# However, for a Django project, 'manage.py' is an entry script and not typically
# part of the "installed" package modules. 'main.py' could be an entry for ASGI/WSGI.
