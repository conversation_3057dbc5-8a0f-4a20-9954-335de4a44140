#!/bin/bash

# 本地Nuitka编译脚本
# 用于在本地主机编译RADIUS服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查环境
check_environment() {
    print_step "检查编译环境..."
    
    # 检查是否在radius目录
    if [[ ! -f "start_async_server.py" ]]; then
        print_error "请在radius目录下运行此脚本"
        exit 1
    fi
    
    # 检查虚拟环境
    if [[ ! -d ".venv" ]]; then
        print_error "虚拟环境 .venv 不存在"
        exit 1
    fi
    
    # 检查Nuitka
    if ! .venv/bin/python -c "import nuitka" 2>/dev/null; then
        print_error "Nuitka未安装，请先安装: uv add nuitka"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 清理旧文件
cleanup_old_files() {
    print_step "清理旧的编译文件..."
    
    rm -rf radius_server.build/
    rm -rf radius_server.dist/
    rm -rf radius_server.onefile-build/
    rm -f radius_server
    rm -f *.so
    
    print_success "清理完成"
}

# 编译函数
compile_radius() {
    print_step "开始Nuitka编译..."
    print_info "这可能需要几分钟时间，请耐心等待..."
    
    # 使用虚拟环境中的python和nuitka
    .venv/bin/python -m nuitka \
        --standalone \
        --onefile \
        --assume-yes-for-downloads \
        --enable-cache \
        --cache-dir=/ssd/ccache \
        --output-filename=radius_server \
        --include-data-dir=conf=conf \
        --include-package=aaaserver \
        --include-package=pyrad \
        --include-package=aiomysql \
        --include-package=mysql.connector \
        --include-package=asyncio \
        --include-package=logging \
        --include-package=configparser \
        --include-package=ipaddress \
        --include-package=pytz \
        --include-package=netaddr \
        --include-package=six \
        --include-package=weakref \
        --include-package=threading \
        --include-package=concurrent.futures \
        --enable-plugin=anti-bloat \
        --remove-output \
        --no-pyi-file \
        --lto=no \
        --jobs=$(nproc) \
        --nofollow-import-to=pytest,unittest,test,tests \
        --nofollow-import-to=setuptools,distutils,pip \
        start_async_server.py
    
    if [[ $? -eq 0 ]]; then
        print_success "编译成功！"
        return 0
    else
        print_error "编译失败"
        return 1
    fi
}

# 验证编译结果
verify_compilation() {
    print_step "验证编译结果..."
    
    if [[ ! -f "radius_server" ]]; then
        print_error "可执行文件 radius_server 不存在"
        return 1
    fi
    
    # 检查文件权限
    chmod +x radius_server
    
    # 显示文件信息
    print_info "可执行文件信息:"
    ls -lh radius_server
    
    # 测试健康检查
    print_info "测试健康检查功能..."
    if ./radius_server --health-check; then
        print_success "健康检查功能正常"
    else
        print_warning "健康检查失败，但这在没有配置文件时是正常的"
    fi
    
    print_success "编译验证完成"
}

# 创建部署包
create_deployment_package() {
    print_step "创建部署包..."
    
    local DEPLOY_DIR="radius_deploy_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$DEPLOY_DIR"
    
    # 复制可执行文件
    cp radius_server "$DEPLOY_DIR/"
    
    # 复制配置文件
    cp -r conf "$DEPLOY_DIR/"
    
    # 创建启动脚本
    cat > "$DEPLOY_DIR/start.sh" << 'EOF'
#!/bin/bash
# RADIUS服务器启动脚本

# 检查配置文件
if [[ ! -f "conf/aaa_config.ini" ]]; then
    echo "错误: 配置文件 conf/aaa_config.ini 不存在"
    exit 1
fi

# 启动服务器
echo "启动RADIUS服务器..."
./radius_server
EOF
    
    chmod +x "$DEPLOY_DIR/start.sh"
    
    # 创建健康检查脚本
    cat > "$DEPLOY_DIR/health_check.sh" << 'EOF'
#!/bin/bash
# RADIUS服务器健康检查脚本

./radius_server --health-check
EOF
    
    chmod +x "$DEPLOY_DIR/health_check.sh"
    
    # 创建README
    cat > "$DEPLOY_DIR/README.md" << 'EOF'
# RADIUS服务器部署包

## 文件说明
- `radius_server`: 编译后的可执行文件
- `conf/`: 配置文件目录
- `start.sh`: 启动脚本
- `health_check.sh`: 健康检查脚本

## 部署步骤
1. 将整个目录复制到目标服务器
2. 修改 `conf/` 目录下的配置文件
3. 运行 `./start.sh` 启动服务

## 健康检查
运行 `./health_check.sh` 检查服务状态

## 端口说明
- 1812/udp: RADIUS认证端口
- 1813/udp: RADIUS计费端口  
- 3799/udp: RADIUS CoA端口
EOF
    
    print_success "部署包已创建: $DEPLOY_DIR"
    print_info "部署包大小: $(du -sh $DEPLOY_DIR | cut -f1)"
}

# 主函数
main() {
    print_info "🚀 RADIUS本地编译脚本启动"
    
    check_environment
    cleanup_old_files
    
    if compile_radius; then
        verify_compilation
        create_deployment_package
        print_success "🎉 编译完成！可执行文件已准备就绪"
        print_info "可以将生成的部署包复制到虚拟机进行部署"
    else
        print_error "❌ 编译失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
