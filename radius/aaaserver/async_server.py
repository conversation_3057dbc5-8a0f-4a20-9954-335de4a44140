#!/usr/bin/python
"""
异步协程版本的RADIUS服务器
支持高并发处理，每个报文使用独立协程处理
"""

import asyncio
import socket
import logging
import logging.config
import configparser
import time
import sys
import os
from datetime import datetime, timedelta
import pytz
from concurrent.futures import ThreadPoolExecutor
import weakref

# 添加项目根目录到Python路径，以便导入本地的pyrad模块
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from pyrad import dictionary, packet, server
from mysql.connector import Error
import ipaddress

# 导入异步数据库工具
from . import async_dbutil

# 导入新的多日志系统
try:
    from . import multi_logger
    # 使用新的多日志系统
    log_info = multi_logger.auth_info
    log_debug = multi_logger.auth_debug
    log_error = multi_logger.auth_error
    log_warning = multi_logger.auth_warning
    start_request = multi_logger.start_request
    end_request = multi_logger.end_request
    log_packet_attributes = multi_logger.log_packet_attributes
    log_auth_step = multi_logger.log_auth_step
    log_database_operation = multi_logger.log_database_operation
    system_error = multi_logger.system_error

    # 预导入计费和CoA日志函数，避免运行时重复导入
    acct_info = multi_logger.acct_info
    acct_error = multi_logger.acct_error
    acct_warning = multi_logger.acct_warning
    coa_info = multi_logger.coa_info
    coa_error = multi_logger.coa_error
    system_info = multi_logger.system_info
    MULTI_LOGGER_AVAILABLE = True

    # 为了兼容性，保留一个logger对象用于系统级日志
    logger = multi_logger.multi_logger.get_logger('system')
except ImportError:
    MULTI_LOGGER_AVAILABLE = False
    # 如果新日志系统不可用，使用传统日志
    try:
        logging.config.fileConfig('conf/logging.conf')
        logger = logging.getLogger(__name__)
    except Exception as e:
        logging.basicConfig(
            level=logging.INFO,
            format='[%(asctime)s] [%(levelname)s] %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/authentication.log')
            ]
        )
        logger = logging.getLogger(__name__)
        logger.warning(f"Failed to load logging config, using basic config: {e}")

    # 提供兼容的日志函数
    log_info = logger.info
    log_debug = logger.debug
    log_error = logger.error
    log_warning = logger.warning
    system_error = logger.error

    def start_request(client_ip, user_name=None, request_type='auth'):
        return f"legacy-{id(asyncio.current_task())}"

    def end_request(result='completed'):
        pass

    def log_packet_attributes(pkt, title="Packet Attributes"):
        logger.info(f"=== {title} ===")
        for attr in pkt.keys():
            logger.info(f"{attr}: {pkt[attr]}")

    def log_auth_step(step, result, details=None):
        logger.info(f"{step}: {result}")

    def log_database_operation(operation, table, duration_ms=None):
        msg = f"DB {operation} on {table}"
        if duration_ms:
            msg += f" ({duration_ms:.2f}ms)"
        logger.debug(msg)

# 读取AAA配置
auth_config = configparser.ConfigParser()
auth_config.read('conf/aaa_config.ini', encoding='utf-8')

# 读取数据库配置
db_config = configparser.ConfigParser()
db_config.read('conf/db.ini', encoding='utf-8')

# 全局变量
vpdn_domains = set()
srv_instance = None


def is_auth_check_enabled(check_type):
    """
    检查指定的认证检查是否启用

    Args:
        check_type (str): 检查类型 ('username_check', 'password_check', 'user_status_check',
                         'online_limit_check', 'domain_check')

    Returns:
        bool: True表示启用，False表示禁用
    """
    try:
        return auth_config.getboolean('authentication', f'{check_type}_enabled')
    except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
        # 如果配置不存在或无效，默认启用
        logger.warning(f"Auth config for {check_type}_enabled not found or invalid, defaulting to enabled")
        return True


def get_auth_config_value(section, key, default_value, value_type='str'):
    """
    获取认证配置值

    Args:
        section (str): 配置段名
        key (str): 配置键名
        default_value: 默认值
        value_type (str): 值类型 ('str', 'int', 'float', 'bool')

    Returns:
        配置值或默认值
    """
    try:
        if value_type == 'int':
            return auth_config.getint(section, key)
        elif value_type == 'float':
            return auth_config.getfloat(section, key)
        elif value_type == 'bool':
            return auth_config.getboolean(section, key)
        else:
            return auth_config.get(section, key)
    except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
        logger.warning(f"Config {section}.{key} not found or invalid, using default: {default_value}")
        return default_value


def safe_get_packet_attr(pkt, attr_name, default_value=None, index=0):
    """
    安全地从RADIUS包中获取属性值

    Args:
        pkt: RADIUS包对象
        attr_name (str): 属性名称
        default_value: 默认值
        index (int): 属性值列表中的索引，默认为0

    Returns:
        属性值或默认值
    """
    try:
        if attr_name in pkt:
            attr_list = pkt[attr_name]
            if isinstance(attr_list, list) and len(attr_list) > index:
                value = attr_list[index]
            elif not isinstance(attr_list, list):
                value = attr_list
            else:
                return default_value

            # 如果是字符串且两端有双引号，则去除双引号
            if isinstance(value, str) and len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]

            return value
        return default_value
    except (KeyError, IndexError, AttributeError, TypeError) as e:
        logger.warning(f"Failed to get packet attribute '{attr_name}': {e}, using default: {default_value}")
        return default_value


def parse_user_name(user_name_value):
    """
    解析用户名，分离用户名和域名

    Args:
        user_name_value (str): 原始用户名值

    Returns:
        tuple: (user_name, user_domain) 用户名和域名的元组
    """
    if not user_name_value:
        return None, None

    # 特殊处理：如果用户名以~开头，去除~和其后第一个字符
    if isinstance(user_name_value, str) and user_name_value.startswith('~') and len(user_name_value) > 2:
        user_name_value = user_name_value[2:]

    if '@' in user_name_value:
        user_name, user_domain = user_name_value.split('@', 1)
        return user_name, user_domain
    else:
        return user_name_value, None


def get_user_name_from_packet(pkt):
    """
    从RADIUS包中提取并解析用户名

    Args:
        pkt: RADIUS包对象

    Returns:
        tuple: (user_name, user_domain) 用户名和域名的元组
    """
    user_name_value = safe_get_packet_attr(pkt, 'User-Name')
    return parse_user_name(user_name_value)


def get_auth_type_from_packet(pkt):
    """
    从RADIUS包中获取认证类型

    Args:
        pkt: RADIUS包对象

    Returns:
        int: 认证类型 (1=CHAP, 0=PAP)
    """
    # 检查是否有CHAP-Password属性
    chap_password = safe_get_packet_attr(pkt, 'CHAP-Password')
    if chap_password:
        return 1  # CHAP
    else:
        return 0  # PAP


def validate_mac_address(mac_address):
    """
    验证MAC地址格式是否正确，支持从复合格式中提取MAC地址

    Args:
        mac_address (str): MAC地址字符串，可能包含额外信息

    Returns:
        str or None: 如果格式正确返回标准化的MAC地址，否则返回None
    """
    if not mac_address or not isinstance(mac_address, str):
        return None

    # 移除所有空白字符
    mac_address = mac_address.strip()

    # 如果是空字符串，返回None
    if not mac_address:
        return None

    import re

    # 首先尝试从复合格式中提取MAC地址
    # 支持格式如: "b083-feb1-4e28 trunk 1/1/1:1033.1022 0/0/0/0/0/0"
    # 使用更简单的方法：直接查找MAC地址模式

    # 尝试匹配各种MAC地址格式，不管后面是否有其他内容
    mac_extraction_patterns = [
        # 标准格式：xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx（开头位置）
        r'^([0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2})',
        # 4位分组格式：xxxx-xxxx-xxxx（开头位置）
        r'^([0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4})',
        # 无分隔符格式：xxxxxxxxxxxx（开头位置）
        r'^([0-9A-Fa-f]{12})',
        # 点分格式：xxxx.xxxx.xxxx（开头位置）
        r'^([0-9A-Fa-f]{4}\.[0-9A-Fa-f]{4}\.[0-9A-Fa-f]{4})'
    ]

    # 尝试从复合格式中提取MAC地址
    for pattern in mac_extraction_patterns:
        match = re.match(pattern, mac_address)
        if match:
            extracted_mac = match.group(1)
            # 递归调用验证提取的MAC地址
            result = validate_pure_mac_address(extracted_mac)
            if result:  # 如果提取的MAC地址有效，返回结果
                return result

    # 如果不是复合格式，直接验证
    return validate_pure_mac_address(mac_address)


def validate_pure_mac_address(mac_address):
    """
    验证纯MAC地址格式（不包含额外信息）

    Args:
        mac_address (str): 纯MAC地址字符串

    Returns:
        str or None: 如果格式正确返回标准化的MAC地址，否则返回None
    """
    if not mac_address or not isinstance(mac_address, str):
        return None

    # 移除所有空白字符
    mac_address = mac_address.strip()

    # 如果是空字符串，返回None
    if not mac_address:
        return None

    # 定义常见的MAC地址格式正则表达式
    mac_patterns = [
        # 标准格式：xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx
        r'^([0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}$',
        # 4位分组格式：xxxx-xxxx-xxxx
        r'^([0-9A-Fa-f]{4}-){2}[0-9A-Fa-f]{4}$',
        # 无分隔符格式：xxxxxxxxxxxx
        r'^[0-9A-Fa-f]{12}$',
        # 点分格式：xxxx.xxxx.xxxx
        r'^([0-9A-Fa-f]{4}\.){2}[0-9A-Fa-f]{4}$'
    ]

    import re

    # 检查是否匹配任何一种格式
    for pattern in mac_patterns:
        if re.match(pattern, mac_address):
            # 标准化MAC地址格式为 xx:xx:xx:xx:xx:xx
            # 移除所有分隔符
            clean_mac = re.sub(r'[:.\-]', '', mac_address.upper())

            # 检查是否全为F（无效MAC地址）
            if clean_mac == 'FFFFFFFFFFFF':
                return None

            # 检查是否全为0（无效MAC地址）
            if clean_mac == '000000000000':
                return None

            # 格式化为标准格式
            formatted_mac = ':'.join([clean_mac[i:i+2] for i in range(0, 12, 2)])
            return formatted_mac

    return None


def verify_user_password(pkt, user_info, encrytype):
    """
    验证用户密码

    Args:
        pkt: RADIUS包对象
        user_info: 用户信息字典
        encrytype: 加密类型 (1=CHAP, 0=PAP)

    Returns:
        int: 验证结果 (0=成功, 2=密码错误)
    """
    try:
        if not isinstance(user_info, dict):
            logger.error("user_info is not a dict")
            return 2

        # 安全地获取用户密码
        user_password = user_info.get('user_password', '')
        logger.info(f"user db password is : {user_password}")

    except Exception as e:
        logger.error(f"Error in verify_user_password setup: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return 2

    if encrytype == 1:
        try:
            # CHAP验证 - 手动实现以避免pyrad库的问题
            chap_password_attr = safe_get_packet_attr(pkt, 'CHAP-Password')
            if chap_password_attr is None:
                logger.warning("No CHAP-Password attribute found in packet")
                return 2

            if len(chap_password_attr) != 17:
                logger.warning(f"Invalid CHAP-Password length: {len(chap_password_attr)}")
                return 2

            # 提取CHAP ID和密码
            chapid = chap_password_attr[0:1]
            chap_response = chap_password_attr[1:]

            # 获取challenge（通常是authenticator）
            challenge = pkt.authenticator

            # 计算期望的CHAP响应
            import hashlib
            if isinstance(user_password, str):
                user_password_bytes = user_password.encode('utf-8')
            else:
                user_password_bytes = user_password

            expected_response = hashlib.md5(chapid + user_password_bytes + challenge).digest()

            if chap_response != expected_response:
                return 2

        except Exception as e:
            system_error(f"Error verifying CHAP password: {e}", exc_info=True)
            return 2
    else:
        try:
            user_passwd = safe_get_packet_attr(pkt, 'User-Password')
            if user_passwd is None:
                logger.warning("No User-Password attribute found in packet")
                return 2
            radius_passwd = pkt.PwDecrypt(user_passwd)
            logger.info(f"radius pap password is : {radius_passwd}")
            if radius_passwd != user_password:
                logger.info(f"radius pap password is not equal db password")
                return 2
        except Exception as e:
            system_error(f"Error verifying PAP password: {e}", exc_info=True)
            return 2

    return 0

# 协程控制配置
MAX_CONCURRENT_TASKS = auth_config.getint('performance', 'max_concurrent_tasks', fallback=1000)
TASK_TIMEOUT = auth_config.getfloat('performance', 'task_timeout_seconds', fallback=30.0)
# 使用 db.ini 中的 pool_size 配置，与其他数据库模块保持一致
DB_POOL_SIZE = db_config.getint('mysql', 'pool_size', fallback=20)

class AsyncRadiusServer:
    """异步RADIUS服务器"""

    def __init__(self, addresses=['0.0.0.0'], auth_port=1812, acct_port=1813,
                 dict_file="conf/dictionary", coa_enabled=None, coa_port=None):
        self.addresses = addresses
        self.auth_port = auth_port
        self.acct_port = acct_port

        # CoA配置
        if coa_enabled is None:
            self.coa_enabled = get_auth_config_value('coa', 'coa_enabled', True, 'bool')
        else:
            self.coa_enabled = coa_enabled

        if coa_port is None:
            self.coa_port = get_auth_config_value('coa', 'coa_port', 3799, 'int')
        else:
            self.coa_port = coa_port

        self.dict = dictionary.Dictionary(dict_file)
        self.hosts = {}

        # 获取主机名
        import socket
        self.hostname = socket.gethostname()

        # 协程控制
        self.semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)
        self.active_tasks = weakref.WeakSet()
        self.stats = {
            'total_requests': 0,
            'active_tasks': 0,
            'max_concurrent': 0,
            'timeouts': 0,
            'errors': 0
        }

        # 数据库线程池（用于同步数据库操作）
        self.db_executor = ThreadPoolExecutor(max_workers=DB_POOL_SIZE)

        # 性能优化：用户信息缓存（BRAS信息已在启动时加载到hosts中）
        self._user_cache = {}  # 用户信息缓存（1分钟TTL）
        self._user_cache_ttl = 60  # 用户缓存TTL 1分钟
        self._last_cache_cleanup = time.time()

        # 性能优化：预编译的正则表达式和常用字符串
        self._common_attrs = {
            'User-Name', 'User-Password', 'CHAP-Password', 'NAS-IP-Address',
            'NAS-Port', 'NAS-Port-Id', 'NAS-Port-Type', 'Calling-Station-Id',
            'Framed-IP-Address', 'Acct-Session-Id', 'Acct-Status-Type',
            'Acct-Session-Time', 'Acct-Input-Octets', 'Acct-Output-Octets'
        }

        # 性能优化：预分配的字典模板
        self._record_template = {
            'user_area': '', 'bras_area': 0, 'user_nat_framedip': '',
            'user_nat_beginport': 0, 'user_nat_endport': 0,
            'user_framedipv6': '', 'user_delegated_ipv6prefix': '',
            'user_ipv6_outoctets': 0, 'user_ipv6_inoctets': 0,
            'user_ipv6_outpackets': 0, 'user_ipv6_inpackets': 0,
            'down_reason': 0, 'client_type': 0
        }

        # 性能优化：预加载授权配置，避免每次认证都读取配置
        self.auth_config = {
            'session_timeout': get_auth_config_value('authorization', 'session_timeout', 172800, 'int'),
            'acct_interim_interval': get_auth_config_value('authorization', 'acct_interim_interval', 7200, 'int'),
            'framed_ip_netmask': get_auth_config_value('authorization', 'framed_ip_netmask', '***************'),
            'service_type': get_auth_config_value('authorization', 'service_type', 2, 'int'),
            'framed_protocol': get_auth_config_value('authorization', 'framed_protocol', 1, 'int')
        }

        # 性能优化：预加载CoA配置，避免每次CoA请求都读取配置
        self.coa_config = {
            'coa_timeout_seconds': get_auth_config_value('coa', 'coa_timeout_seconds', 10.0, 'float'),
            'default_coa_response': get_auth_config_value('coa', 'default_coa_response', 'ack'),
            'default_disconnect_response': get_auth_config_value('coa', 'default_disconnect_response', 'ack')
        }

        logger.info(f"AsyncRadiusServer initialized with hostname={self.hostname}, max_concurrent_tasks={MAX_CONCURRENT_TASKS}")
        logger.info(f"Authorization config loaded: session_timeout={self.auth_config['session_timeout']}, "
                   f"acct_interim_interval={self.auth_config['acct_interim_interval']}, "
                   f"service_type={self.auth_config['service_type']}")
        logger.info(f"CoA config loaded: timeout={self.coa_config['coa_timeout_seconds']}s, "
                   f"default_coa_response={self.coa_config['default_coa_response']}, "
                   f"default_disconnect_response={self.coa_config['default_disconnect_response']}")

    def _cleanup_cache(self):
        """清理过期的用户缓存条目"""
        current_time = time.time()
        if current_time - self._last_cache_cleanup > 60:  # 每分钟清理一次
            # 清理用户缓存（1分钟TTL）
            expired_keys = [k for k, (_, timestamp) in self._user_cache.items()
                          if current_time - timestamp > self._user_cache_ttl]
            for key in expired_keys:
                del self._user_cache[key]

            self._last_cache_cleanup = current_time

    async def _get_user_info_cached(self, user_name, user_domain):
        """获取用户信息（带缓存，1分钟TTL）"""        

        self._cleanup_cache()
        cache_key = f"{user_name}@{user_domain or ''}"

        # 检查缓存
        if cache_key in self._user_cache:
            user_info, timestamp = self._user_cache[cache_key]
            if time.time() - timestamp < self._user_cache_ttl:
                return user_info

        # 缓存未命中，从数据库获取
        user_info = await async_dbutil.get_user_info(user_name, user_domain)
        self._user_cache[cache_key] = (user_info, time.time())
        return user_info

    def _fast_get_packet_attrs(self, pkt, attr_names):
        """快速批量获取报文属性"""
        result = {}
        for attr_name in attr_names:
            if attr_name in pkt:
                try:
                    value = pkt[attr_name]
                    if isinstance(value, list) and len(value) == 1:
                        value = value[0]
                    # 快速去除双引号
                    if isinstance(value, str) and len(value) >= 2 and value[0] == '"' and value[-1] == '"':
                        value = value[1:-1]
                    result[attr_name] = value
                except (IndexError, AttributeError):
                    result[attr_name] = None
            else:
                result[attr_name] = None
        return result

    async def start_server(self):
        """启动异步服务器"""
        tasks = []

        # 启动认证服务器
        for addr in self.addresses:
            auth_server = await asyncio.start_server(
                self._handle_connection, addr, self.auth_port
            )
            tasks.append(auth_server)
            logger.info(f"Auth server listening on {addr}:{self.auth_port}")

            # 启动计费服务器
            acct_server = await asyncio.start_server(
                self._handle_connection, addr, self.acct_port
            )
            tasks.append(acct_server)
            logger.info(f"Acct server listening on {addr}:{self.acct_port}")

        # 启动统计任务
        asyncio.create_task(self._stats_reporter())

        # 等待所有服务器
        await asyncio.gather(*[server.serve_forever() for server in tasks])

    async def _handle_connection(self, reader, writer):
        """处理UDP连接（实际上我们需要UDP服务器）"""
        # 这里需要改为UDP处理
        pass

    async def start_udp_servers(self):
        """启动UDP服务器"""
        loop = asyncio.get_running_loop()
        transports = []

        # 启动认证UDP服务器
        for addr in self.addresses:
            auth_transport, auth_protocol = await loop.create_datagram_endpoint(
                lambda: AsyncRadiusProtocol(self, 'auth'),
                local_addr=(addr, self.auth_port)
            )
            transports.append(auth_transport)
            logger.info(f"Auth UDP server listening on {addr}:{self.auth_port}")

            # 启动计费UDP服务器
            acct_transport, acct_protocol = await loop.create_datagram_endpoint(
                lambda: AsyncRadiusProtocol(self, 'acct'),
                local_addr=(addr, self.acct_port)
            )
            transports.append(acct_transport)
            logger.info(f"Acct UDP server listening on {addr}:{self.acct_port}")

            # 启动CoA UDP服务器（如果启用）
            if self.coa_enabled:
                coa_transport, coa_protocol = await loop.create_datagram_endpoint(
                    lambda: AsyncRadiusProtocol(self, 'coa'),
                    local_addr=(addr, self.coa_port)
                )
                transports.append(coa_transport)
                logger.info(f"CoA UDP server listening on {addr}:{self.coa_port}")

        # 启动统计任务
        asyncio.create_task(self._stats_reporter())

        # 保持服务器运行
        try:
            await asyncio.Future()  # 永远等待
        except KeyboardInterrupt:
            logger.info("Server shutdown requested")
        finally:
            # 关闭所有传输
            for transport in transports:
                transport.close()
            self.db_executor.shutdown(wait=True)

    async def _stats_reporter(self):
        """定期报告统计信息"""
        while True:
            await asyncio.sleep(60)  # 每分钟报告一次
            active_count = len(self.active_tasks)
            self.stats['active_tasks'] = active_count
            self.stats['max_concurrent'] = max(self.stats['max_concurrent'], active_count)

            # 使用系统日志记录器输出统计信息
            try:
                from . import multi_logger
                multi_logger.system_info(f"📊 Server Stats: Total={self.stats['total_requests']}, "
                                       f"Active={active_count}, Max={self.stats['max_concurrent']}, "
                                       f"Timeouts={self.stats['timeouts']}, Errors={self.stats['errors']}")
            except ImportError:
                # 如果多日志系统不可用，使用标准logger
                logger.info(f"📊 Server Stats: Total={self.stats['total_requests']}, "
                           f"Active={active_count}, Max={self.stats['max_concurrent']}, "
                           f"Timeouts={self.stats['timeouts']}, Errors={self.stats['errors']}")

class AsyncRadiusProtocol(asyncio.DatagramProtocol):
    """异步RADIUS协议处理器"""

    def __init__(self, server, server_type):
        self.server = server
        self.server_type = server_type  # 'auth', 'acct', or 'coa'
        self.transport = None

    def connection_made(self, transport):
        self.transport = transport

    def datagram_received(self, data, addr):
        """接收到UDP数据报"""
        # 为每个报文创建协程任务
        task = asyncio.create_task(self._process_packet_async(data, addr))
        self.server.active_tasks.add(task)

        # 更新统计
        self.server.stats['total_requests'] += 1

    async def _process_packet_async(self, data, addr):
        """异步处理RADIUS报文"""
        async with self.server.semaphore:  # 限制并发数
            try:
                # 设置超时
                await asyncio.wait_for(
                    self._handle_radius_packet(data, addr),
                    timeout=TASK_TIMEOUT
                )
            except asyncio.TimeoutError:
                self.server.stats['timeouts'] += 1
                logger.warning(f"Packet processing timeout from {addr}")
            except Exception as e:
                self.server.stats['errors'] += 1
                logger.error(f"Error processing packet from {addr}: {e}")

    async def _handle_radius_packet(self, data, addr):
        """处理RADIUS报文的核心逻辑"""
        try:
            # 解析RADIUS报文
            if self.server_type == 'auth':
                pkt = packet.AuthPacket(packet=data, dict=self.server.dict)
                await self._handle_auth_packet_async(pkt, addr)
            elif self.server_type == 'acct':
                pkt = packet.AcctPacket(packet=data, dict=self.server.dict)
                await self._handle_acct_packet_async(pkt, addr)
            elif self.server_type == 'coa':
                pkt = packet.CoAPacket(packet=data, dict=self.server.dict)
                await self._handle_coa_packet_async(pkt, addr)

        except Exception as e:
            logger.error(f"Error parsing RADIUS packet from {addr}: {e}")

    async def _handle_auth_packet_async(self, pkt, addr):
        """异步处理认证报文"""
        pkt.source = addr

        # 验证客户端
        if not self._verify_client(pkt):
            log_warning(f"Unknown client: {addr[0]}")
            return

        # 获取用户信息并开始请求追踪
        user_name, user_domain = get_user_name_from_packet(pkt)
        client_ip = safe_get_packet_attr(pkt, 'NAS-IP-Address', addr[0])
        req_id = start_request(client_ip, user_name, 'auth')

        try:
            log_info(f"Received an authentication request from {addr[0]}:{addr[1]}")

            # 记录包属性
            log_packet_attributes(pkt, "Authentication Request Attributes")

            # 异步处理认证逻辑
            reply = await self._process_authentication(pkt)

            # 发送回复
            self._send_reply(reply, addr)

            # 异步记录到数据库
            asyncio.create_task(self._record_auth_async(pkt, reply))

            # 根据结果结束请求追踪
            if reply.code == packet.AccessAccept:
                end_request('success')
            else:
                end_request('rejected')

        except Exception as e:
            log_error(f"❌ Authentication processing failed: {e}")

            # 创建错误回复，确保协程不会因为异常而退出
            try:
                error_reply = pkt.CreateReply()
                error_reply.code = packet.AccessReject
                error_reply["Reply-Message"] = "Internal server error"
                self._send_reply(error_reply, addr)
                log_info("Sent error reply due to processing exception")
            except Exception as reply_error:
                log_error(f"Failed to send error reply: {reply_error}")

            end_request('error')
            # 不再抛出异常，让协程正常结束

    async def _handle_acct_packet_async(self, pkt, addr):
        """异步处理计费报文"""
        pkt.source = addr

        # 验证客户端
        if not self._verify_client(pkt):
            # 使用计费日志记录           
            multi_logger.acct_warning(f"Unknown client: {addr[0]}")
            return

        # 获取用户信息并开始请求追踪
        user_name, user_domain = get_user_name_from_packet(pkt)
        client_ip = safe_get_packet_attr(pkt, 'NAS-IP-Address', addr[0])
        req_id = start_request(client_ip, user_name, 'acct')

        try:
            # 使用计费日志记录           
            multi_logger.acct_info(f"Received accounting request from {addr[0]}:{addr[1]}")

            # 创建回复
            reply = pkt.CreateReply()

            # 发送回复
            self._send_reply(reply, addr)

            # 异步处理计费逻辑
            asyncio.create_task(self._process_accounting_async(pkt))

            end_request('success')
        except Exception as e:
            multi_logger.acct_error(f"Error processing accounting packet: {e}")

            # 创建错误回复，确保协程不会因为异常而退出
            try:
                error_reply = pkt.CreateReply()
                # 计费回复通常是Accounting-Response
                self._send_reply(error_reply, addr)
                multi_logger.acct_info("Sent error reply due to processing exception")
            except Exception as reply_error:
                multi_logger.acct_error(f"Failed to send error reply: {reply_error}")

            end_request('error')
            # 不再抛出异常，让协程正常结束

    async def _handle_coa_packet_async(self, pkt, addr):
        """异步处理CoA报文"""
        pkt.source = addr

        # 验证客户端
        if not self._verify_client(pkt):
            # 使用CoA日志记录
            if MULTI_LOGGER_AVAILABLE:
                coa_error(f"Unknown client: {addr[0]}")
            else:
                logger.error(f"Unknown CoA client: {addr[0]}")
            return

        # 获取用户信息并开始请求追踪
        user_name = safe_get_packet_attr(pkt, 'User-Name', '----')
        session_id = safe_get_packet_attr(pkt, 'Acct-Session-Id', '----')
        client_ip = safe_get_packet_attr(pkt, 'NAS-IP-Address', addr[0])
        req_id = start_request(client_ip, user_name, 'coa')

        try:
            # 使用CoA日志记录
            if MULTI_LOGGER_AVAILABLE:
                coa_info(f"Received CoA request from {addr[0]}:{addr[1]}")

                # 记录包属性
                coa_info("**************************************** [Attributes] ****************************************")
                for attr in pkt.keys():
                    try:
                        value = pkt[attr]
                        coa_info(f"{attr}: {value}")
                    except Exception as e:
                        coa_info(f"{attr}: <error reading value: {e}>")
            else:
                logger.info(f"Received CoA request from {addr[0]}:{addr[1]}")
                logger.info("**************************************** [Attributes] ****************************************")
                for attr in pkt.keys():
                    try:
                        value = pkt[attr]
                        logger.info(f"{attr}: {value}")
                    except Exception as e:
                        logger.info(f"{attr}: <error reading value: {e}>")

            # 处理CoA请求
            reply = await self._process_coa_request(pkt)

            # 发送回复
            self._send_reply(reply, addr)

            # 异步记录CoA操作到数据库
            asyncio.create_task(self._record_coa_async(pkt, reply, addr))

            # 记录处理结果
            if reply.code in [packet.CoAACK, packet.DisconnectACK]:
                if MULTI_LOGGER_AVAILABLE:
                    coa_info(f"CoA request processed successfully, response code: {reply.code}")
                else:
                    logger.info(f"CoA request processed successfully, response code: {reply.code}")
                end_request('success')
            else:
                if MULTI_LOGGER_AVAILABLE:
                    coa_info(f"CoA request failed, response code: {reply.code}")
                else:
                    logger.info(f"CoA request failed, response code: {reply.code}")
                end_request('failed')

        except Exception as e:
            if MULTI_LOGGER_AVAILABLE:
                coa_error(f"Error processing CoA packet: {e}")
            else:
                logger.error(f"Error processing CoA packet: {e}")

            # 创建错误回复，确保协程不会因为异常而退出
            try:
                error_reply = pkt.CreateReply()
                error_reply.code = packet.CoANAK
                error_reply["Reply-Message"] = "Internal server error"
                self._send_reply(error_reply, addr)
                if MULTI_LOGGER_AVAILABLE:
                    coa_info("Sent error reply due to processing exception")
                else:
                    logger.info("Sent CoA error reply due to processing exception")
            except Exception as reply_error:
                if MULTI_LOGGER_AVAILABLE:
                    coa_error(f"Failed to send error reply: {reply_error}")
                else:
                    logger.error(f"Failed to send CoA error reply: {reply_error}")

            end_request('error')
            # 不再抛出异常，让协程正常结束

    def _verify_client(self, pkt):
        """验证RADIUS客户端"""
        client_ip = pkt.source[0]
        if client_ip in self.server.hosts:
            pkt.secret = self.server.hosts[client_ip].secret
            return True
        elif '0.0.0.0' in self.server.hosts:
            pkt.secret = self.server.hosts['0.0.0.0'].secret
            return True
        return False

    def _send_reply(self, reply, addr):
        """发送RADIUS回复"""
        try:
            reply_data = reply.ReplyPacket()
            self.transport.sendto(reply_data, addr)
        except Exception as e:
            logger.error(f"Error sending reply to {addr}: {e}")

    def _fast_get_packet_attrs(self, pkt, attr_names):
        """快速批量获取报文属性"""
        result = {}
        for attr_name in attr_names:
            if attr_name in pkt:
                try:
                    value = pkt[attr_name]
                    if isinstance(value, list) and len(value) == 1:
                        value = value[0]
                    # 快速去除双引号
                    if isinstance(value, str) and len(value) >= 2 and value[0] == '"' and value[-1] == '"':
                        value = value[1:-1]
                    result[attr_name] = value
                except (IndexError, AttributeError):
                    result[attr_name] = None
            else:
                result[attr_name] = None
        return result

    async def _process_coa_request(self, pkt):
        """异步处理CoA请求的核心逻辑"""
        from . import multi_logger

        try:
            # 性能优化：使用预加载的CoA配置
            coa_timeout = self.server.coa_config['coa_timeout_seconds']

            multi_logger.coa_info("**************************************** [processing] ****************************************")

            # 获取会话信息
            session_id = safe_get_packet_attr(pkt, 'Acct-Session-Id')
            user_name = safe_get_packet_attr(pkt, 'User-Name')

            if session_id:
                multi_logger.coa_info(f"Session ID: {session_id}")
            if user_name:
                multi_logger.coa_info(f"User Name: {user_name}")

            # 检查报文类型并处理
            if pkt.code == packet.CoARequest:
                multi_logger.coa_info("Processing CoA Request")
                reply = await self._handle_coa_change_request(pkt, session_id, user_name, coa_timeout)

            elif pkt.code == packet.DisconnectRequest:
                multi_logger.coa_info("Processing Disconnect Request")
                reply = await self._handle_disconnect_request(pkt, session_id, user_name, coa_timeout)

            else:
                # 未知的CoA报文类型
                multi_logger.coa_error(f"Unknown CoA packet code: {pkt.code}")
                reply = pkt.CreateReply()
                reply.code = packet.CoANAK
                reply["Reply-Message"] = "Unknown CoA packet type"

            # 记录最终响应属性
            multi_logger.coa_info("**************************************** [response] ****************************************")
            multi_logger.coa_info(f"Response Code: {reply.code}")
            for attr_name in reply.keys():
                try:
                    attr_value = reply[attr_name]
                    if isinstance(attr_value, list) and len(attr_value) == 1:
                        attr_value = attr_value[0]
                    multi_logger.coa_info(f"  {attr_name}: {attr_value}")
                except Exception as e:
                    multi_logger.coa_info(f"  {attr_name}: <error reading value: {e}>")

            return reply

        except Exception as e:
            multi_logger.coa_error(f"Error processing CoA request: {e}")
            # 创建错误回复
            reply = pkt.CreateReply()
            reply.code = packet.CoANAK
            reply["Reply-Message"] = "Internal server error"
            return reply

    async def _handle_coa_change_request(self, pkt, session_id, user_name, timeout):
        """处理CoA变更请求"""
        from . import multi_logger

        try:
            # 创建基础回复
            reply = pkt.CreateReply()

            # 如果有会话ID，查找对应的在线记录
            if session_id:
                online_session = await async_dbutil.get_online_session_by_id(session_id)
                if online_session:
                    nas_ip = online_session['bras_ip']
                    multi_logger.coa_info(f"Found online session for {session_id}, NAS IP: {nas_ip}")

                    # 转发CoA请求到NAS
                    nas_response = await self._forward_coa_to_nas(pkt, nas_ip, timeout)
                    if nas_response:
                        # 使用NAS的响应
                        reply.code = nas_response.code
                        # 复制NAS响应的属性
                        for attr in nas_response.keys():
                            reply[attr] = nas_response[attr]
                        multi_logger.coa_info(f"CoA forwarded to NAS {nas_ip}, response code: {nas_response.code}")
                    else:
                        reply.code = packet.CoANAK
                        reply["Reply-Message"] = "Failed to forward CoA to NAS"
                        multi_logger.coa_error(f"Failed to forward CoA to NAS {nas_ip}")
                else:
                    reply.code = packet.CoANAK
                    reply["Reply-Message"] = "Session not found"
                    multi_logger.coa_info(f"Session {session_id} not found in online records")
            else:
                # 没有会话ID，按配置处理
                default_response = self.server.coa_config['default_coa_response']
                if default_response.lower() == 'ack':
                    reply.code = packet.CoAACK
                    multi_logger.coa_info("CoA Request accepted by policy (no session ID)")
                else:
                    reply.code = packet.CoANAK
                    reply["Reply-Message"] = "CoA Request rejected by policy"
                    multi_logger.coa_info("CoA Request rejected by policy (no session ID)")

            return reply

        except Exception as e:
            multi_logger.coa_error(f"Error handling CoA change request: {e}")
            reply = pkt.CreateReply()
            reply.code = packet.CoANAK
            reply["Reply-Message"] = "Internal server error"
            return reply

    async def _handle_disconnect_request(self, pkt, session_id, user_name, timeout):
        """处理断开连接请求"""
        try:
            # 创建基础回复
            reply = pkt.CreateReply()

            # 如果有会话ID，查找对应的在线记录
            if session_id:
                online_session = await async_dbutil.get_online_session_by_id(session_id)
                if online_session:
                    nas_ip = online_session['bras_ip']
                    multi_logger.coa_info(f"Found online session for {session_id}, NAS IP: {nas_ip}")

                    # 转发Disconnect请求到NAS
                    nas_response = await self._forward_disconnect_to_nas(pkt, nas_ip, timeout)
                    if nas_response:
                        # 使用NAS的响应
                        reply.code = nas_response.code
                        # 复制NAS响应的属性
                        for attr in nas_response.keys():
                            reply[attr] = nas_response[attr]                            
                            multi_logger.coa_info(f"  {attr}: {nas_response[attr]}")

                        multi_logger.coa_info(f"Disconnect forwarded to NAS {nas_ip}, response code: {nas_response.code}")

                        # 如果NAS确认断开连接成功，从在线记录中删除
                        # if nas_response.code == packet.DisconnectACK:
                        #     try:
                        #         # 异步删除在线记录
                        #         asyncio.create_task(self._remove_online_session_async(session_id))
                        #         multi_logger.coa_info(f"Scheduled removal of online session {session_id}")
                        #     except Exception as e:
                        #         multi_logger.coa_error(f"Error scheduling session removal: {e}")
                    else:
                        reply.code = packet.DisconnectNAK
                        reply["Reply-Message"] = "Failed to forward Disconnect to NAS"
                        multi_logger.coa_error(f"Failed to forward Disconnect to NAS {nas_ip}")
                else:
                    reply.code = packet.DisconnectNAK
                    reply["Reply-Message"] = "Session not found"
                    multi_logger.coa_info(f"Session {session_id} not found in online records")
            elif user_name:
                # 没有会话ID但有用户名，查找用户的所有在线会话
                user_name_parsed, user_domain = parse_user_name(user_name)
                online_sessions = await async_dbutil.get_online_sessions_by_user(user_name_parsed, user_domain)

                if online_sessions:
                    multi_logger.coa_info(f"Found {len(online_sessions)} online sessions for user {user_name}")
                    success_count = 0

                    # 对每个会话发送断开连接请求
                    for session in online_sessions:
                        session_nas_ip = session['bras_ip']
                        session_id_current = session['session_id']

                        # 创建针对特定会话的断开连接请求
                        disconnect_pkt = self._create_disconnect_packet_for_session(pkt, session_id_current)
                        nas_response = await self._forward_disconnect_to_nas(disconnect_pkt, session_nas_ip, timeout)

                        if nas_response and nas_response.code == packet.DisconnectACK:
                            success_count += 1
                            # 异步删除在线记录
                            # asyncio.create_task(self._remove_online_session_async(session_id_current))
                            multi_logger.coa_info(f"Successfully disconnected session {session_id_current}")
                        else:
                            multi_logger.coa_error(f"Failed to disconnect session {session_id_current}")

                    # 根据成功断开的会话数量设置响应
                    if success_count == len(online_sessions):
                        reply.code = packet.DisconnectACK
                        reply["Reply-Message"] = f"All {success_count} sessions disconnected"
                        multi_logger.coa_info(f"All {success_count} sessions disconnected for user {user_name}")
                    elif success_count > 0:
                        reply.code = packet.DisconnectACK
                        reply["Reply-Message"] = f"{success_count}/{len(online_sessions)} sessions disconnected"
                        multi_logger.coa_info(f"Partial success: {success_count}/{len(online_sessions)} sessions disconnected")
                    else:
                        reply.code = packet.DisconnectNAK
                        reply["Reply-Message"] = "Failed to disconnect any sessions"
                        multi_logger.coa_error("Failed to disconnect any sessions")
                else:
                    reply.code = packet.DisconnectNAK
                    reply["Reply-Message"] = "No online sessions found for user"
                    multi_logger.coa_info(f"No online sessions found for user {user_name}")
            else:
                # 没有会话ID也没有用户名，按配置处理
                default_response = self.server.coa_config['default_disconnect_response']
                if default_response.lower() == 'ack':
                    reply.code = packet.DisconnectACK
                    multi_logger.coa_info("Disconnect Request accepted by policy (no session/user info)")
                else:
                    reply.code = packet.DisconnectNAK
                    reply["Reply-Message"] = "Disconnect Request rejected by policy"
                    multi_logger.coa_info("Disconnect Request rejected by policy (no session/user info)")

            return reply

        except Exception as e:
            multi_logger.coa_error(f"Error handling disconnect request: {e}")
            reply = pkt.CreateReply()
            reply.code = packet.DisconnectNAK
            reply["Reply-Message"] = "Internal server error"
            return reply

    async def _forward_coa_to_nas(self, pkt, nas_ip, timeout):
        """转发CoA请求到NAS设备"""
        from . import multi_logger

        try:
            # 获取NAS设备信息
            nas_info = await self._get_nas_info_async(nas_ip)
            if not nas_info:
                multi_logger.coa_error(f"NAS {nas_ip} not found in configuration")
                return None

            # 在线程池中执行同步的CoA客户端操作
            loop = asyncio.get_event_loop()
            nas_response = await loop.run_in_executor(
                self.server.db_executor,
                self._send_coa_to_nas_sync,
                pkt, nas_ip, nas_info['bras_secret'], timeout
            )

            return nas_response

        except Exception as e:
            multi_logger.coa_error(f"Error forwarding CoA to NAS {nas_ip}: {e}")
            return None

    async def _forward_disconnect_to_nas(self, pkt, nas_ip, timeout):
        """转发Disconnect请求到NAS设备"""

        try:
            # 获取NAS设备信息
            nas_info = await self._get_nas_info_async(nas_ip)
            if not nas_info:
                multi_logger.coa_error(f"NAS {nas_ip} not found in configuration")
                return None

            # 在线程池中执行同步的Disconnect客户端操作
            loop = asyncio.get_event_loop()
            nas_response = await loop.run_in_executor(
                self.server.db_executor,
                self._send_disconnect_to_nas_sync,
                pkt, nas_ip, nas_info['bras_secret'], timeout
            )

            return nas_response

        except Exception as e:
            multi_logger.coa_error(f"Error forwarding Disconnect to NAS {nas_ip}: {e}")
            return None

    def _send_coa_to_nas_sync(self, pkt, nas_ip, nas_secret, timeout):
        """同步发送CoA请求到NAS（在线程池中执行）"""
        try:
            from pyrad.client import Client
            from pyrad import dictionary

            # 创建CoA客户端
            client = Client(server=nas_ip, secret=nas_secret.encode() if isinstance(nas_secret, str) else nas_secret,
                          dict=dictionary.Dictionary("conf/dictionary"))
            client.timeout = timeout

            # 创建CoA请求包
            coa_request = client.CreateCoAPacket()
            coa_request.code = packet.CoARequest

            # 复制原始包的属性
            for attr in pkt.keys():
                try:
                    coa_request[attr] = pkt[attr]
                except Exception as e:
                    logger.warning(f"Failed to copy attribute {attr}: {e}")

            # 发送请求并获取响应
            response = client.SendPacket(coa_request)
            return response

        except Exception as e:
            logger.error(f"Error sending CoA to NAS {nas_ip}: {e}")
            return None

    def _send_disconnect_to_nas_sync(self, pkt, nas_ip, nas_secret, timeout):
        """同步发送Disconnect请求到NAS（在线程池中执行）"""
        try:
            from pyrad.client import Client
            from pyrad import dictionary

            # 创建CoA客户端
            client = Client(server=nas_ip, secret=nas_secret.encode() if isinstance(nas_secret, str) else nas_secret,
                          dict=dictionary.Dictionary("conf/dictionary"))
            client.timeout = timeout

            # 创建Disconnect请求包
            disconnect_request = client.CreateCoAPacket(code=packet.DisconnectRequest)

            # 复制原始包的属性
            for attr in pkt.keys():
                try:
                    disconnect_request[attr] = pkt[attr]
                except Exception as e:
                    logger.warning(f"Failed to copy attribute {attr}: {e}")

            # 发送请求并获取响应
            response = client.SendPacket(disconnect_request)
            return response

        except Exception as e:
            logger.error(f"Error sending Disconnect to NAS {nas_ip}: {e}")
            return None

    async def _get_nas_info_async(self, nas_ip):
        """异步获取NAS设备信息"""
        try:
            # 首先检查服务器的hosts配置
            if nas_ip in self.server.hosts:
                nas_host = self.server.hosts[nas_ip]
                return {
                    'bras_ip': nas_ip,
                    'bras_secret': nas_host.secret,
                    'bras_vendor': getattr(nas_host, 'vendor', 'Unknown'),
                    'bras_model': getattr(nas_host, 'model', 'Unknown')
                }

            # 如果不在hosts中，从数据库查询
            loop = asyncio.get_event_loop()
            nas_info = await loop.run_in_executor(
                self.server.db_executor,
                self._get_nas_from_db_sync,
                nas_ip
            )

            return nas_info

        except Exception as e:
            logger.error(f"Error getting NAS info for {nas_ip}: {e}")
            return None

    def _get_nas_from_db_sync(self, nas_ip):
        """从数据库同步获取NAS信息（在线程池中执行）"""
        try:
            return async_dbutil.get_bras_by_ip(nas_ip)  # 使用异步版本 dbutil.get_bras_by_ip(nas_ip)
        except Exception as e:
            logger.error(f"Error getting NAS from database: {e}")
            return None

    def _create_disconnect_packet_for_session(self, original_pkt, session_id):
        """为特定会话创建断开连接包"""
        try:
            # 创建新的断开连接包
            disconnect_pkt = packet.CoAPacket(dict=self.server.dict)
            disconnect_pkt.code = packet.DisconnectRequest

            # 复制原始包的属性，但使用指定的会话ID
            for attr in original_pkt.keys():
                if attr != 'Acct-Session-Id':  # 会话ID单独设置
                    try:
                        disconnect_pkt[attr] = original_pkt[attr]
                    except Exception as e:
                        logger.warning(f"Failed to copy attribute {attr}: {e}")

            # 设置会话ID
            disconnect_pkt['Acct-Session-Id'] = session_id

            return disconnect_pkt

        except Exception as e:
            logger.error(f"Error creating disconnect packet for session {session_id}: {e}")
            return original_pkt

    async def _remove_online_session_async(self, session_id):
        """异步删除在线会话记录"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.server.db_executor,
                self._remove_online_session_sync,
                session_id
            )

            if result:
                from . import multi_logger
                multi_logger.coa_info(f"Successfully removed online session {session_id}")
            else:
                from . import multi_logger
                multi_logger.coa_error(f"Failed to remove online session {session_id}")

        except Exception as e:
            from . import multi_logger
            multi_logger.coa_error(f"Error removing online session {session_id}: {e}")

    def _remove_online_session_sync(self, session_id):
        """同步删除在线会话记录（在线程池中执行）"""
        try:            
            return async_dbutil.delete_onlinerecord_by_sessionid(session_id)  # 使用异步版本 dbutil.delete_onlinerecord_by_sessionid(session_id)
        except Exception as e:
            logger.error(f"Error removing online session from database: {e}")
            return False

    async def _process_authentication(self, pkt):
        """异步处理认证逻辑（性能优化版本）"""
        now = datetime.now(pytz.timezone('Asia/Shanghai'))

        # 性能优化：批量获取认证所需的所有属性
        auth_attrs = self._fast_get_packet_attrs(pkt, [
            'NAS-IP-Address', 'NAS-Port', 'NAS-Port-Type',
            'NAS-Port-Id', 'Calling-Station-Id', 'User-Name', 'Acct-Session-Id'
        ])

        # 安全地处理NAS-Port
        bras_port = None
        bras_port_raw = auth_attrs.get('NAS-Port')
        if bras_port_raw is not None:
            try:
                bras_port = int(bras_port_raw)
            except (ValueError, TypeError):
                logger.warning(f"Invalid NAS-Port value: {bras_port_raw}")
                bras_port = None

        # MAC地址提取逻辑 - 优先使用厂商特定属性
        validated_mac = None

        # 首先尝试从华为厂商特定属性中获取（优先级最高）
        huawei_user_mac = safe_get_packet_attr(pkt, 'Huawei-User-Mac')
        if huawei_user_mac:
            validated_mac = validate_mac_address(huawei_user_mac)
            if validated_mac:
                log_info(f"MAC address extracted from Huawei-User-Mac: {validated_mac}")

        # 如果没有Huawei-User-Mac，尝试从Huawei-IPHost-Addr获取
        if not validated_mac:
            huawei_iphost_addr = safe_get_packet_attr(pkt, 'Huawei-IPHost-Addr')
            if huawei_iphost_addr and ' ' in huawei_iphost_addr:
                try:
                    # 格式为"A.B.C.D hh:hh:hh:hh:hh:hh"
                    parts = huawei_iphost_addr.split(' ')
                    if len(parts) >= 2:
                        mac_addr = parts[1]
                        validated_mac = validate_mac_address(mac_addr)
                        if validated_mac:
                            log_info(f"MAC address extracted from Huawei-IPHost-Addr: {validated_mac}")
                except Exception as e:
                    logger.warning(f"Error extracting MAC from Huawei-IPHost-Addr during auth: {e}")

        # 最后才使用Calling-Station-Id（优先级最低）
        if not validated_mac:
            raw_mac = auth_attrs.get('Calling-Station-Id')
            validated_mac = validate_mac_address(raw_mac)
            if validated_mac:
                log_info(f"MAC address extracted from Calling-Station-Id: {validated_mac}")
            elif raw_mac:
                log_warning(f"Invalid MAC address format in Calling-Station-Id: {raw_mac}, will not store MAC in database")

        # 获取会话ID并记录日志
        session_id = auth_attrs.get('Acct-Session-Id')
        log_info(f"Session ID from packet: {session_id}")

        record = {
            'bras_ip': auth_attrs.get('NAS-IP-Address'),
            'bras_port': bras_port,
            'bras_port_type': auth_attrs.get('NAS-Port-Type'),
            'line_info': auth_attrs.get('NAS-Port-Id'),
            'mac': validated_mac,  # 使用验证后的MAC地址，无效时为None
            'radius_server': self.server.hostname,
            'auth_date': now,
            'session_id': session_id  # 添加会话ID
        }

        user_name, user_domain = get_user_name_from_packet(pkt)
        record['user_name'] = user_name
        record['dail_user_name'] = user_name
        record['user_domain'] = user_domain or ''

        # 开始认证阶段
        log_info("**************************************** [authenticate] ****************************************")

        # 检测认证类型
        encrytype = get_auth_type_from_packet(pkt)
        auth_type_name = 'chap' if encrytype == 1 else 'pap'
        log_info(f"radius password encrypt type is {auth_type_name} (auto-detected)")

        # 域名存在性验证
        if user_domain and is_auth_check_enabled('domain_check'):
            if user_domain not in vpdn_domains:
                errorcode = 5  # 域名不存在
                record['user_business_type'] = 0
                log_info(f"domain check failed! domain '{user_domain}' does not exist in vpdn table")

                # 记录部分
                log_info("**************************************** [recording] ****************************************")

                # 创建回复并记录
                reply = await self._create_auth_reply(pkt, errorcode, None)
                record['auth_result_code'] = errorcode

                # 设置user_area字段（域名验证失败时用户不存在，使用BRAS区域）
                await self._set_user_area_for_auth_record(record, None)

                # 异步记录认证结果
                asyncio.create_task(self._insert_authrecord_with_error_handling(record))
                log_info("Authentication result insertion task created")
                return reply
            else:
                log_info(f"domain check passed! domain: {user_domain}")
        elif user_domain and not is_auth_check_enabled('domain_check'):
            log_info(f"domain check disabled, skipping domain validation for '{user_domain}'")
        else:
            log_info("domain check skipped (no domain)")

        # 用户名验证
        errorcode = 0
        user_info = None

        if is_auth_check_enabled('username_check'):
            # 性能优化：使用缓存查询用户信息
            start_time = time.time()
            user_info = await self.server._get_user_info_cached(user_name, user_domain)
            db_duration = (time.time() - start_time) * 1000
            log_database_operation('SELECT', 'user', db_duration)

            if not user_info:
                errorcode = 1
                record['user_business_type'] = 0
                log_info("user name check failed!")
            else:
                log_info("user name check passed!")
                record['user_business_type'] = user_info['user_business_type']

                # 检查用户状态
                if is_auth_check_enabled('user_status_check'):
                    user_status = user_info.get('user_status', 4)  # 默认为4（用状态错）
                    if user_status == 1:
                        log_info(f"user status check passed! user_status: {user_status}")
                    elif user_status == 2:
                        errorcode = 6  # 用户暂停
                        log_info(f"user status check failed! user_status: {user_status} (user paused)")
                    elif user_status == 4:
                        errorcode = 7  # 用户欠费
                        log_info(f"user status check failed! user_status: {user_status} (user arrears)")
                    else:
                        errorcode = 4  # 其他用户状态错误
                        log_info(f"user status check failed! user_status: {user_status}, only status 1 is allowed")
                else:
                    log_info("user status check skipped (disabled)")

                # 密码验证
                if errorcode == 0 and is_auth_check_enabled('password_check'):
                    # 直接在当前协程中执行密码验证（不需要新的协程）
                    try:
                        start_time = time.time()
                        errorcode = verify_user_password(pkt, user_info, encrytype)
                        pwd_duration = (time.time() - start_time) * 1000

                        if errorcode == 0:
                            log_info("password check passed!")
                        else:
                            log_info("password check failed!")
                    except Exception as e:
                        system_error(f"Error in password verification: {e}", exc_info=True)
                        errorcode = 2
                elif errorcode == 0:
                    log_info("password check skipped (disabled)")

                # 在线数量限制检查
                if errorcode == 0 and is_auth_check_enabled('online_limit_check'):
                    start_time = time.time()
                    online_count = await async_dbutil.get_online_count_by_user(
                        user_name, user_domain, user_info['user_business_type']
                    )
                    online_duration = (time.time() - start_time) * 1000
                    log_database_operation('SELECT', 'onlinerecord', online_duration)

                    limit = user_info.get('user_allow_onlinenums', 0)
                    if online_count > limit:
                        errorcode = 3
                        log_info(f"online limit check failed! online_count: {online_count}, limit: {limit}")
                    else:
                        log_info(f"online limit check passed! online_count: {online_count}, limit: {limit}")
                elif errorcode == 0:
                    log_info("online limit check skipped (disabled)")
        else:
            log_info("username check skipped (disabled)")
            user_info = {'user_bind_ip': None, 'user_business_type': 0}
            record['user_business_type'] = 0

        # 认证结果总结
        if errorcode == 0:
            log_info("auth result : all passed!")

        # 创建回复
        try:
            reply = await self._create_auth_reply(pkt, errorcode, user_info)
        except Exception as e:
            log_error(f"Error creating auth reply: {e}")
            import traceback
            log_error(f"Traceback: {traceback.format_exc()}")
            raise
        record['auth_result_code'] = errorcode

        # 如果认证成功，收集授权属性
        if errorcode == 0 and reply.code == packet.AccessAccept:
            try:
                # 从回复包中提取授权属性，直接使用字符串值
                record['service_type'] = reply.get('Service-Type', [None])[0] if 'Service-Type' in reply else None
                record['framed_protocol'] = reply.get('Framed-Protocol', [None])[0] if 'Framed-Protocol' in reply else None

                record['framed_ip_netmask'] = reply.get('Framed-IP-Netmask', [None])[0] if 'Framed-IP-Netmask' in reply else None
                record['session_timeout'] = reply.get('Session-Timeout', [None])[0] if 'Session-Timeout' in reply else None
                record['acct_interim_interval'] = reply.get('Acct-Interim-Interval', [None])[0] if 'Acct-Interim-Interval' in reply else None
                record['framed_ip_address'] = reply.get('Framed-IP-Address', [None])[0] if 'Framed-IP-Address' in reply else None

                # 从用户信息中提取带宽信息
                if user_info:
                    record['user_down_bandwidth'] = user_info.get('user_down_bandwidth')
                    record['user_up_bandwidth'] = user_info.get('user_up_bandwidth')
                    log_info(f"User bandwidth - Down: {record.get('user_down_bandwidth')} kbps, Up: {record.get('user_up_bandwidth')} kbps")

                log_info("Authorization attributes collected for database storage")
            except Exception as e:
                log_error(f"Error collecting authorization attributes: {e}")

        # 记录部分
        log_info("**************************************** [recording] ****************************************")

        # 设置user_area字段
        await self._set_user_area_for_auth_record(record, user_info)

        # 异步记录认证结果
        asyncio.create_task(self._insert_authrecord_with_error_handling(record))
        log_info("Authentication result insertion task created")

        return reply

    async def _set_user_area_for_auth_record(self, record, user_info):
        """设置认证记录的user_area字段"""
        try:
            if user_info and user_info.get('user_area'):
                # 如果认证的用户存在，使用user.user_area
                record['user_area'] = user_info['user_area']
                log_info(f"Set user_area from user info: {record['user_area']}")
            else:
                # 如果认证的用户不存在，使用bras.bras_area
                bras_ip = record.get('bras_ip')
                if bras_ip:
                    bras_area = await async_dbutil.get_bras_area_by_ip(bras_ip)
                    if bras_area:
                        record['user_area'] = bras_area
                        log_info(f"Set user_area from BRAS info: {record['user_area']}")
                    else:
                        record['user_area'] = ''
                        log_warning(f"BRAS area not found for IP: {bras_ip}")
                else:
                    record['user_area'] = ''
                    log_warning("No BRAS IP found in record")
        except Exception as e:
            log_error(f"Error setting user_area for auth record: {e}")
            record['user_area'] = ''  # 设置默认值

    async def _insert_authrecord_with_error_handling(self, record):
        """带错误处理的异步插入认证记录"""
        try:
            log_info(f"Attempting to insert auth record for user: {record.get('user_name')}")
            result = await async_dbutil.insert_authrecord(record)
            if result is None:
                log_error("❌ Database operation failed (exception caught by decorator)")
            elif result:
                log_info("✅ Authentication result successfully inserted into authrecord table")
            else:
                log_error("❌ Failed to insert authentication result into authrecord table (no rows affected)")
        except Exception as e:
            log_error(f"❌ Exception inserting authentication result: {e}")
            import traceback
            log_error(f"Traceback: {traceback.format_exc()}")

    async def _create_auth_reply(self, pkt, errorcode, user_info):
        """创建认证回复"""
        try:
            reply = pkt.CreateReply()
        except Exception as e:
            log_error(f"Error creating reply packet: {e}")
            raise

        if errorcode == 0:
            log_info("**************************************** [authorise] ****************************************")

            # 性能优化：使用预加载的授权配置
            session_timeout = self.server.auth_config['session_timeout']
            acct_interim_interval = self.server.auth_config['acct_interim_interval']
            framed_ip_netmask = self.server.auth_config['framed_ip_netmask']
            service_type = self.server.auth_config['service_type']
            framed_protocol = self.server.auth_config['framed_protocol']

            # 基础授权属性
            try:
                reply["Service-Type"] = service_type
                reply["Framed-Protocol"] = framed_protocol
                reply["Framed-IP-Netmask"] = framed_ip_netmask
                reply["Session-Timeout"] = session_timeout
                reply["Acct-Interim-Interval"] = acct_interim_interval

                # 打印基础授权属性
                log_info(f"Service-Type: {service_type}")
                log_info(f"Framed-Protocol: {framed_protocol}")
                log_info(f"Framed-IP-Netmask: {framed_ip_netmask}")
                log_info(f"Session-Timeout: {session_timeout}")
                log_info(f"Acct-Interim-Interval: {acct_interim_interval}")

            except Exception as e:
                log_error(f"Error adding authorization attributes: {e}")
                raise
            reply.code = packet.AccessAccept

            # 处理Framed-IP-Address
            if user_info and user_info.get('user_bind_ip'):
                framed_ip = user_info['user_bind_ip']
                reply["Framed-IP-Address"] = framed_ip
                log_info(f"author framedip : {framed_ip}")
            else:
                framed_ip = '***************'
                reply["Framed-IP-Address"] = framed_ip
                log_info(f"author framedip : {framed_ip}")

            # 处理厂商特定属性
            bras_ip = safe_get_packet_attr(pkt, 'NAS-IP-Address')
            if bras_ip and bras_ip in self.server.hosts and user_info:
                try:
                    bras_vendor = self.server.hosts[bras_ip].vendor
                    log_info(f"bras vendor is {bras_vendor}")

                    if bras_vendor == 'Huawei':
                        up_bandwidth = user_info.get('user_up_bandwidth', 0)
                        down_bandwidth = user_info.get('user_down_bandwidth', 0)
                        if up_bandwidth and down_bandwidth:
                            reply["Huawei-Input-Average-Rate"] = int(up_bandwidth) * 1024
                            reply["Huawei-Output-Average-Rate"] = int(down_bandwidth) * 1024
                            log_info(f"author up bandwidth : {int(up_bandwidth) * 1024}")
                            log_info(f"author down bandwidth : {int(down_bandwidth) * 1024}")

                        # 处理华为域名属性
                        hw_domain_name = safe_get_packet_attr(pkt, 'HW-Domain-Name')
                        if hw_domain_name:
                            reply["HW-Domain-Name"] = hw_domain_name
                            log_info(f"author HW-Domain-Name : {hw_domain_name}")

                    elif bras_vendor == 'H3C' and user_info:
                        up_bandwidth = user_info.get('user_up_bandwidth', 0)
                        down_bandwidth = user_info.get('user_down_bandwidth', 0)
                        if up_bandwidth and down_bandwidth:
                            # H3C使用bps作为单位，直接使用kbps * 1000转换为bps
                            reply["Input-Average-Rate"] = int(up_bandwidth) * 1000
                            reply["Output-Average-Rate"] = int(down_bandwidth) * 1000
                            log_info(f"author H3C up bandwidth : {int(up_bandwidth) * 1000} bps")
                            log_info(f"author H3C down bandwidth : {int(down_bandwidth) * 1000} bps")

                    elif bras_vendor == 'ZTE' and user_info:
                        down_bandwidth = user_info.get('user_down_bandwidth', 0)
                        up_bandwidth = user_info.get('user_up_bandwidth', 0)
                        if down_bandwidth and up_bandwidth:
                            reply["ZTE-Rate-Ctrl-Scr-Down"] = int(down_bandwidth)
                            reply["ZTE-Rate-Ctrl-Scr-Up"] = int(up_bandwidth)
                            log_info(f"author ZTE-Rate-Ctrl-Scr-Down : {int(down_bandwidth)}")
                            log_info(f"author ZTE-Rate-Ctrl-Scr-Up : {int(up_bandwidth)}")

                except Exception as e:
                    log_warning(f"Error processing vendor attributes: {e}")
            elif bras_ip:
                log_warning(f"BRAS IP {bras_ip} not found in hosts configuration")

            # 打印所有最终的授权属性
            log_info("Final authorization attributes:")
            for attr_name in reply.keys():
                try:
                    attr_value = reply[attr_name]
                    if isinstance(attr_value, list) and len(attr_value) == 1:
                        attr_value = attr_value[0]
                    log_info(f"  {attr_name}: {attr_value}")
                except Exception as e:
                    log_info(f"  {attr_name}: <error reading value: {e}>")

        else:
            # 处理各种错误情况
            error_messages = {
                1: "UserName_Err",
                2: "Password_Err",
                3: "LimitUser_Err",
                4: "UserStatus_Err",
                5: "Domain_Not_Exist",
                6: "UserStatus_Paused",
                7: "UserStatus_Arrears"
            }
            message = error_messages.get(errorcode, "Unknown_Error")
            reply["Reply-Message"] = message
            reply.code = packet.AccessReject
            log_info(f"auth result : {message}")

        return reply

    async def _record_auth_async(self, pkt, reply):
        """异步记录认证结果"""
        loop = asyncio.get_running_loop()
        try:
            # 在线程池中执行数据库操作
            await loop.run_in_executor(
                self.server.db_executor,
                self._record_auth_sync,
                pkt, reply
            )
        except Exception as e:
            logger.error(f"Error recording auth result: {e}")

    def _record_auth_sync(self, pkt, reply):
        """同步记录认证结果（在线程池中执行）"""
        # 这里实现数据库记录逻辑
        pass

    async def _process_accounting_async(self, pkt):
        """异步处理计费逻辑"""
        try:
            # 性能优化：使用预导入的日志函数
            if MULTI_LOGGER_AVAILABLE:
                acct_info("**************************************** [Attributes] ****************************************")
                for attr in pkt.keys():
                    try:
                        value = pkt[attr]
                        acct_info(f"{attr}: {value}")
                    except Exception as e:
                        acct_info(f"{attr}: <error reading value: {e}>")

                acct_info("**************************************** [accounting] ****************************************")
            else:
                # 如果多日志系统不可用，使用标准logger
                logger.info("**************************************** [Attributes] ****************************************")
                for attr in pkt.keys():
                    logger.info("%s: %s" % (attr, pkt[attr]))
                logger.info("**************************************** [accounting] ****************************************")

            now = datetime.now(pytz.timezone('Asia/Shanghai'))

            # 性能优化：批量获取计费相关的所有属性
            acct_attrs = self._fast_get_packet_attrs(pkt, [
                'Acct-Status-Type', 'Acct-Session-Time', 'Acct-Delay-Time', 'NAS-Port',
                'Acct-Output-Octets', 'Acct-Input-Octets', 'Acct-Input-Gigawords',
                'Acct-Output-Gigawords', 'Acct-Output-Packets', 'Acct-Input-Packets',
                'NAS-Port-Id', 'Calling-Station-Id', 'NAS-IP-Address', 'NAS-Port-Type',
                'Acct-Session-Id', 'Framed-IP-Address', 'Acct-Terminate-Cause'
            ])

            acct_type = acct_attrs.get('Acct-Status-Type')
            session_time = acct_attrs.get('Acct-Session-Time', 0)
            delay_time = acct_attrs.get('Acct-Delay-Time', 0)
            nas_port_raw = acct_attrs.get('NAS-Port', 0)

            # 安全转换数值
            try:
                session_time = int(session_time) if session_time is not None else 0
                delay_time = int(delay_time) if delay_time is not None else 0
                nas_port = int(nas_port_raw) if nas_port_raw is not None else 0
            except (ValueError, TypeError):
                logger.warning(f"Invalid numeric values in accounting packet")
                session_time = 0
                delay_time = 0
                nas_port = 0

            # 性能优化：安全地获取流量统计，使用预获取的属性
            try:
                output_octets = int(acct_attrs.get('Acct-Output-Octets', 0))
                input_octets = int(acct_attrs.get('Acct-Input-Octets', 0))
                input_gigawords = int(acct_attrs.get('Acct-Input-Gigawords', 0))
                output_gigawords = int(acct_attrs.get('Acct-Output-Gigawords', 0))
                output_packets = int(acct_attrs.get('Acct-Output-Packets', 0))
                input_packets = int(acct_attrs.get('Acct-Input-Packets', 0))
            except (ValueError, TypeError):
                logger.debug(f"Invalid traffic statistics in accounting packet")
                output_octets = 0
                input_octets = 0
                input_gigawords = 0
                output_gigawords = 0
                output_packets = 0
                input_packets = 0

            # MAC地址提取逻辑 - 优先使用厂商特定属性
            validated_mac = None

            # 首先尝试从华为厂商特定属性中获取（优先级最高）
            huawei_user_mac = safe_get_packet_attr(pkt, 'Huawei-User-Mac')
            if huawei_user_mac:
                validated_mac = validate_mac_address(huawei_user_mac)
                if validated_mac:
                    if MULTI_LOGGER_AVAILABLE:
                        acct_info(f"MAC address extracted from Huawei-User-Mac: {validated_mac}")
                    else:
                        logger.info(f"MAC address extracted from Huawei-User-Mac: {validated_mac}")

            # 如果没有Huawei-User-Mac，尝试从Huawei-IPHost-Addr获取
            if not validated_mac:
                huawei_iphost_addr = safe_get_packet_attr(pkt, 'Huawei-IPHost-Addr')
                if huawei_iphost_addr and ' ' in huawei_iphost_addr:
                    try:
                        # 格式为"A.B.C.D hh:hh:hh:hh:hh:hh"
                        parts = huawei_iphost_addr.split(' ')
                        if len(parts) >= 2:
                            mac_addr = parts[1]
                            validated_mac = validate_mac_address(mac_addr)
                            if validated_mac:
                                if MULTI_LOGGER_AVAILABLE:
                                    acct_info(f"MAC address extracted from Huawei-IPHost-Addr: {validated_mac}")
                                else:
                                    logger.info(f"MAC address extracted from Huawei-IPHost-Addr: {validated_mac}")
                    except Exception as e:
                        logger.warning(f"Error extracting MAC from Huawei-IPHost-Addr during accounting: {e}")

            # 最后才使用Calling-Station-Id（优先级最低）
            if not validated_mac:
                raw_mac = acct_attrs.get('Calling-Station-Id', '')
                validated_mac = validate_mac_address(raw_mac)
                if validated_mac:
                    if MULTI_LOGGER_AVAILABLE:
                        acct_info(f"MAC address extracted from Calling-Station-Id: {validated_mac}")
                    else:
                        logger.info(f"MAC address extracted from Calling-Station-Id: {validated_mac}")
                elif raw_mac:
                    if MULTI_LOGGER_AVAILABLE:
                        acct_warning(f"Invalid MAC address format in Calling-Station-Id: {raw_mac}, will not store MAC in database")
                    else:
                        logger.warning(f"Invalid MAC address format in Calling-Station-Id: {raw_mac}, will not store MAC in database")

            # 性能优化：构建基础记录，使用预获取的属性
            record = {
                'online_time': now - timedelta(seconds=session_time) - timedelta(seconds=delay_time),
                'offline_time': now,
                'duration': session_time + delay_time,
                'line_info': acct_attrs.get('NAS-Port-Id', ''),
                'mac': validated_mac or '',  # 使用验证后的MAC地址，无效时为空字符串
                'bras_ip': acct_attrs.get('NAS-IP-Address', ''),
                'bras_port': nas_port,
                'bras_port_type': acct_attrs.get('NAS-Port-Type', ''),
                'session_id': acct_attrs.get('Acct-Session-Id', ''),
                'client_type': 0,
                'packet_process_time': now,
                'user_framedip': acct_attrs.get('Framed-IP-Address', ''),
                'user_ipv4_outoctets': output_octets + (4 * input_gigawords * 1024 * 1024 * 1024),
                'user_ipv4_inoctets': input_octets + (4 * output_gigawords * 1024 * 1024 * 1024),
                'user_ipv4_outpackets': output_packets,
                'user_ipv4_inpackets': input_packets,
                'packet_type': acct_type,
                'radius_server': self.server.hostname,
                # 初始化所有必需的字段
                'user_area': '',
                'bras_area': 0,
                'user_nat_framedip': '',
                'user_nat_beginport': 0,
                'user_nat_endport': 0,
                'user_framedipv6': '',
                'user_delegated_ipv6prefix': '',
                'user_ipv6_outoctets': 0,
                'user_ipv6_inoctets': 0,
                'user_ipv6_outpackets': 0,
                'user_ipv6_inpackets': 0,
                'down_reason': 0
            }

            # 获取用户信息
            user_name, user_domain = get_user_name_from_packet(pkt)
            record['user_name'] = user_name
            record['dail_user_name'] = user_name
            record['user_domain'] = user_domain or ''

            # 处理IPv6相关属性
            self._process_ipv6_attributes(pkt, record)

            # 性能优化：使用缓存获取用户信息
            user_info = await self.server._get_user_info_cached(user_name, user_domain)
            if not user_info:
                record['user_business_type'] = 0
                record['down_reason'] = 9999
                if MULTI_LOGGER_AVAILABLE:
                    acct_info("can not find user in db")
                else:
                    logger.info("can not find user in db")
            else:
                record['user_business_type'] = user_info.get('user_business_type', 0)
                record['down_reason'] = acct_attrs.get('Acct-Terminate-Cause')
                record['user_area'] = user_info.get('user_area', '')

            # 性能优化：处理BRAS相关信息
            acct_update_record = await self._process_bras_attributes_async(pkt, record, now)

            # 根据计费类型执行相应操作
            session_id = acct_attrs.get('Acct-Session-Id')

            # 记录处理步骤
            try:
                multi_logger.acct_info(f"Processing {acct_type} request for user {user_name}, session {session_id}")
                multi_logger.acct_info(f"User business type: {record['user_business_type']}, area: {record.get('user_area', 'N/A')}")
                multi_logger.acct_info(f"BRAS IP: {record.get('bras_ip', 'N/A')}, area: {record.get('bras_area', 'N/A')}")

                # 检查是否有华为地址更新标志
                huawei_update_flag = safe_get_packet_attr(pkt, 'Huawei-Acct-Update-Address', 0)
                if huawei_update_flag == 1:
                    multi_logger.acct_info("Huawei address update packet detected")
                    # 显示华为NAT信息
                    nat_public_addr = safe_get_packet_attr(pkt, 'Huawei-NAT-Public-Address')
                    nat_start_port = safe_get_packet_attr(pkt, 'Huawei-NAT-Start-Port')
                    nat_end_port = safe_get_packet_attr(pkt, 'Huawei-NAT-End-Port')

                    if nat_public_addr:
                        multi_logger.acct_info(f"Huawei-NAT-Public-Address: {nat_public_addr}")
                    if nat_start_port:
                        multi_logger.acct_info(f"Huawei-NAT-Start-Port: {nat_start_port}")
                    if nat_end_port:
                        multi_logger.acct_info(f"Huawei-NAT-End-Port: {nat_end_port}")

                if acct_type == 'Stop':
                    multi_logger.acct_info(f"Session duration: {record['duration']} seconds")
                    multi_logger.acct_info(f"IPv4 traffic - In: {record.get('user_ipv4_inoctets', 0)} bytes, Out: {record.get('user_ipv4_outoctets', 0)} bytes")
                    if record.get('user_ipv6_inoctets', 0) > 0 or record.get('user_ipv6_outoctets', 0) > 0:
                        multi_logger.acct_info(f"IPv6 traffic - In: {record.get('user_ipv6_inoctets', 0)} bytes, Out: {record.get('user_ipv6_outoctets', 0)} bytes")
                    if record.get('user_nat_framedip'):
                        multi_logger.acct_info(f"NAT info - Public IP: {record.get('user_nat_framedip')}, Port range: {record.get('user_nat_beginport', 0)}-{record.get('user_nat_endport', 0)}")
            except ImportError:
                logger.info(f"Processing {acct_type} request for user {user_name}, session {session_id}")

            await self._execute_accounting_operation_async(acct_type, record, acct_update_record, session_id)

        except Exception as e:
            logger.error(f"Error in accounting processing: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _process_accounting_sync(self, pkt):
        """同步处理计费逻辑（在线程池中执行）
        注意：此方法已被 _process_accounting_async 替代，不再使用
        """
        try:
            
            logging.info("%s [Attributes] %s", 40 * "*", 40 * "*")
            for attr in pkt.keys():
                logging.info("%s: %s" % (attr, pkt[attr]))

            logging.info("%s [accounting]  %s", 40 * "*", 40 * "*")

            now = datetime.now(pytz.timezone('Asia/Shanghai'))
            acct_type = safe_get_packet_attr(pkt, 'Acct-Status-Type')

            # 安全地获取计费相关的数值属性
            session_time = safe_get_packet_attr(pkt, 'Acct-Session-Time', 0)
            delay_time = safe_get_packet_attr(pkt, 'Acct-Delay-Time', 0)
            nas_port_raw = safe_get_packet_attr(pkt, 'NAS-Port', 0)

            # 安全转换数值
            try:
                session_time = int(session_time) if session_time is not None else 0
                delay_time = int(delay_time) if delay_time is not None else 0
                nas_port = int(nas_port_raw) if nas_port_raw is not None else 0
            except (ValueError, TypeError):
                logger.warning(f"Invalid numeric values in accounting packet")
                session_time = 0
                delay_time = 0
                nas_port = 0

            # 安全地获取流量统计
            try:
                output_octets = int(safe_get_packet_attr(pkt, 'Acct-Output-Octets', 0))
                input_octets = int(safe_get_packet_attr(pkt, 'Acct-Input-Octets', 0))
                input_gigawords = int(safe_get_packet_attr(pkt, 'Acct-Input-Gigawords', 0))
                output_gigawords = int(safe_get_packet_attr(pkt, 'Acct-Output-Gigawords', 0))
                output_packets = int(safe_get_packet_attr(pkt, 'Acct-Output-Packets', 0))
                input_packets = int(safe_get_packet_attr(pkt, 'Acct-Input-Packets', 0))
            except (ValueError, TypeError):
                logger.debug(f"Invalid traffic statistics in accounting packet")
                output_octets = 0
                input_octets = 0
                input_gigawords = 0
                output_gigawords = 0
                output_packets = 0
                input_packets = 0

            # MAC地址提取逻辑 - 优先使用厂商特定属性
            validated_mac = None

            # 首先尝试从华为厂商特定属性中获取（优先级最高）
            huawei_user_mac = safe_get_packet_attr(pkt, 'Huawei-User-Mac')
            if huawei_user_mac:
                validated_mac = validate_mac_address(huawei_user_mac)
                if validated_mac:
                    logger.info(f"MAC address extracted from Huawei-User-Mac: {validated_mac}")

            # 如果没有Huawei-User-Mac，尝试从Huawei-IPHost-Addr获取
            if not validated_mac:
                huawei_iphost_addr = safe_get_packet_attr(pkt, 'Huawei-IPHost-Addr')
                if huawei_iphost_addr and ' ' in huawei_iphost_addr:
                    try:
                        # 格式为"A.B.C.D hh:hh:hh:hh:hh:hh"
                        parts = huawei_iphost_addr.split(' ')
                        if len(parts) >= 2:
                            mac_addr = parts[1]
                            validated_mac = validate_mac_address(mac_addr)
                            if validated_mac:
                                logger.info(f"MAC address extracted from Huawei-IPHost-Addr: {validated_mac}")
                    except Exception as e:
                        logger.warning(f"Error extracting MAC from Huawei-IPHost-Addr during sync accounting: {e}")

            # 最后才使用Calling-Station-Id（优先级最低）
            if not validated_mac:
                raw_mac = safe_get_packet_attr(pkt, 'Calling-Station-Id')
                validated_mac = validate_mac_address(raw_mac)
                if validated_mac:
                    logger.info(f"MAC address extracted from Calling-Station-Id: {validated_mac}")
                elif raw_mac:
                    logger.warning(f"Invalid MAC address format in Calling-Station-Id: {raw_mac}, will not store MAC in database")

            # 构建基础记录
            record = {
                'online_time': now - timedelta(seconds=session_time) - timedelta(seconds=delay_time),
                'offline_time': now,
                'duration': session_time + delay_time,
                'line_info': safe_get_packet_attr(pkt, 'NAS-Port-Id'),
                'mac': validated_mac or '',  # 使用验证后的MAC地址，无效时为空字符串
                'bras_ip': safe_get_packet_attr(pkt, 'NAS-IP-Address'),
                'bras_port': nas_port,
                'bras_port_type': safe_get_packet_attr(pkt, 'NAS-Port-Type'),
                'session_id': safe_get_packet_attr(pkt, 'Acct-Session-Id'),
                'client_type': 0,
                'packet_process_time': now,
                'user_framedip': safe_get_packet_attr(pkt, 'Framed-IP-Address'),
                'user_ipv4_outoctets': output_octets + (4 * input_gigawords * 1024 * 1024 * 1024),
                'user_ipv4_inoctets': input_octets + (4 * output_gigawords * 1024 * 1024 * 1024),
                'user_ipv4_outpackets': output_packets,
                'user_ipv4_inpackets': input_packets,
                'packet_type': acct_type,
                'radius_server': self.server.hostname
            }

            # 获取用户信息
            user_name, user_domain = get_user_name_from_packet(pkt)
            record['user_name'] = user_name
            record['dail_user_name'] = user_name
            record['user_domain'] = user_domain or ''

            # 处理IPv6相关属性
            self._process_ipv6_attributes(pkt, record)

            # 获取用户信息（同步版本，因为这是同步函数）
            try:
                import dbutil                
                user_info = dbutil.get_user_info(user_name, user_domain)
                if not user_info:
                    record['user_business_type'] = 0
                    record['down_reason'] = 9999
                    logger.info("can not find user in db")
                else:
                    record['user_business_type'] = user_info.get('user_business_type', 0)
                    record['down_reason'] = safe_get_packet_attr(pkt, 'Acct-Terminate-Cause')
                    record['user_area'] = user_info.get('user_area', '')
            except Exception as e:
                logger.warning(f"Error getting user info: {e}")
                record['user_business_type'] = 0
                record['down_reason'] = 9999

            # 处理BRAS相关信息
            acct_update_record = self._process_bras_attributes(pkt, record, now)

            # 根据计费类型执行相应操作
            session_id = safe_get_packet_attr(pkt, 'Acct-Session-Id')
            self._execute_accounting_operation(acct_type, record, acct_update_record, session_id)

        except Exception as e:
            logger.error(f"Error in accounting processing: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _process_ipv6_attributes(self, pkt, record):
        """处理IPv6相关属性"""
        try:
            import ipaddress

            # 安全地处理IPv6相关属性
            framed_interface_id = safe_get_packet_attr(pkt, 'Framed-Interface-Id')
            ipv6_prefix = safe_get_packet_attr(pkt, 'Framed-IPv6-Prefix')
            user_delegated_ipv6prefix = safe_get_packet_attr(pkt, 'Delegated-IPv6-Prefix')

            if ipv6_prefix:
                try:
                    ipv6_network = ipaddress.IPv6Network(ipv6_prefix, strict=False)
                    if framed_interface_id:
                        try:
                            formatted_interface_id = framed_interface_id.hex().upper()
                            if len(formatted_interface_id) == 16:
                                formatted_interface_id = ':'.join([formatted_interface_id[i:i + 4] for i in range(0, 16, 4)])
                            network_address = str(ipv6_network.network_address)[:-1]
                            ipv6_address = f"{network_address}{formatted_interface_id}"
                            record['user_framedipv6'] = str(ipaddress.IPv6Address(ipv6_address))
                        except (AttributeError, ipaddress.AddressValueError) as e:
                            logger.warning(f"Error processing IPv6 interface ID: {e}")
                except (ipaddress.AddressValueError, TypeError) as e:
                    logger.warning(f"Invalid Framed-IPv6-Prefix: {e}")

            if user_delegated_ipv6prefix:
                record['user_delegated_ipv6prefix'] = user_delegated_ipv6prefix

        except Exception as e:
            logger.warning(f"Error processing IPv6 attributes: {e}")

    async def _process_bras_attributes_async(self, pkt, record, now):
        """异步处理BRAS相关属性（性能优化版本）"""
        try:
            # 性能优化：批量获取常用属性
            bras_attrs = self._fast_get_packet_attrs(pkt, [
                'NAS-IP-Address', 'Huawei-NAT-Public-Address', 'Huawei-NAT-Start-Port',
                'Huawei-NAT-End-Port', 'Huawei-Acct-Update-Address',
                'Huawei-Acct-IPv6-Input-Octets', 'Huawei-Acct-IPv6-Input-Gigawords',
                'Huawei-Acct-IPv6-Output-Octets', 'Huawei-Acct-IPv6-Output-Gigawords',
                'Huawei-Acct-IPv6-Input-Packets', 'Huawei-Acct-IPv6-Output-Packets',
                'ZTE-NAT-IP-Address', 'ZTE-NAT-start-Port', 'ZTE-NAT-end-Port',
                'ZTE-Acct-Address-Change'
            ])

            bras_ip = bras_attrs.get('NAS-IP-Address')
            bras = None
            acct_update_flag = 0

            # 首先尝试从hosts配置获取BRAS信息
            if bras_ip and bras_ip in self.server.hosts:
                try:
                    bras = self.server.hosts[bras_ip]
                    if MULTI_LOGGER_AVAILABLE:
                        acct_info(f"bras vendor is : {bras.vendor}")
                    else:
                        logger.info("bras vendor is : %s", bras.vendor)

                    area_value = getattr(bras, 'area', 0)
                    record['bras_area'] = area_value

                    if bras.vendor == 'Huawei':
                        # 性能优化：使用预获取的属性
                        nat_public_addr = bras_attrs.get('Huawei-NAT-Public-Address')
                        if nat_public_addr:
                            record['user_nat_framedip'] = nat_public_addr

                        record['user_nat_beginport'] = bras_attrs.get('Huawei-NAT-Start-Port', 0)
                        record['user_nat_endport'] = bras_attrs.get('Huawei-NAT-End-Port', 0)

                        # 从华为设备的厂商特定属性中提取MAC地址
                        # 优先使用Huawei-User-Mac属性
                        huawei_user_mac = safe_get_packet_attr(pkt, 'Huawei-User-Mac')
                        if huawei_user_mac:
                            validated_mac = validate_mac_address(huawei_user_mac)
                            if validated_mac:
                                record['mac'] = validated_mac
                                if MULTI_LOGGER_AVAILABLE:
                                    acct_info(f"Huawei MAC address extracted from Huawei-User-Mac: {validated_mac}")
                                else:
                                    logger.info(f"Huawei MAC address extracted from Huawei-User-Mac: {validated_mac}")

                        # 如果没有Huawei-User-Mac，尝试从Huawei-IPHost-Addr中提取
                        if 'mac' not in record or not record['mac']:
                            huawei_iphost_addr = safe_get_packet_attr(pkt, 'Huawei-IPHost-Addr')
                            if huawei_iphost_addr and ' ' in huawei_iphost_addr:
                                try:
                                    # 格式为"A.B.C.D hh:hh:hh:hh:hh:hh"
                                    parts = huawei_iphost_addr.split(' ')
                                    if len(parts) >= 2:
                                        mac_addr = parts[1]
                                        validated_mac = validate_mac_address(mac_addr)
                                        if validated_mac:
                                            record['mac'] = validated_mac
                                            if MULTI_LOGGER_AVAILABLE:
                                                acct_info(f"Huawei MAC address extracted from Huawei-IPHost-Addr: {validated_mac}")
                                            else:
                                                logger.info(f"Huawei MAC address extracted from Huawei-IPHost-Addr: {validated_mac}")
                                except Exception as e:
                                    logger.warning(f"Error extracting MAC from Huawei-IPHost-Addr: {e}")

                        # 性能优化：安全地处理IPv6流量统计，使用预获取的属性
                        try:
                            ipv6_input_octets = int(bras_attrs.get('Huawei-Acct-IPv6-Input-Octets', 0))
                            ipv6_input_gigawords = int(bras_attrs.get('Huawei-Acct-IPv6-Input-Gigawords', 0))
                            ipv6_output_octets = int(bras_attrs.get('Huawei-Acct-IPv6-Output-Octets', 0))
                            ipv6_output_gigawords = int(bras_attrs.get('Huawei-Acct-IPv6-Output-Gigawords', 0))

                            record['user_ipv6_inoctets'] = ipv6_input_octets + (4 * ipv6_input_gigawords * 1024 * 1024 * 1024)
                            record['user_ipv6_outoctets'] = ipv6_output_octets + (4 * ipv6_output_gigawords * 1024 * 1024 * 1024)
                            record['user_ipv6_inpackets'] = int(bras_attrs.get('Huawei-Acct-IPv6-Input-Packets', 0))
                            record['user_ipv6_outpackets'] = int(bras_attrs.get('Huawei-Acct-IPv6-Output-Packets', 0))
                            acct_update_flag = int(bras_attrs.get('Huawei-Acct-Update-Address', 0))
                            # 将更新标志存储到record中以便后续使用
                            record['Huawei-Acct-Update-Address'] = acct_update_flag
                        except (ValueError, TypeError):
                            logger.debug("Invalid Huawei IPv6 statistics values")
                            record['user_ipv6_inoctets'] = 0
                            record['user_ipv6_outoctets'] = 0
                            record['user_ipv6_inpackets'] = 0
                            record['user_ipv6_outpackets'] = 0

                    elif bras.vendor == 'H3C':
                        # 处理H3C设备的NAT和IPv6属性
                        h3c_nat_addr = safe_get_packet_attr(pkt, 'NAT-IP-Address')
                        if h3c_nat_addr:
                            record['user_nat_framedip'] = h3c_nat_addr

                        record['user_nat_beginport'] = safe_get_packet_attr(pkt, 'NAT-Start-Port', 0)
                        record['user_nat_endport'] = safe_get_packet_attr(pkt, 'NAT-End-Port', 0)

                        # 从Ip-Host-Addr属性中提取MAC地址
                        ip_host_addr = safe_get_packet_attr(pkt, 'Ip-Host-Addr')
                        if ip_host_addr and ' ' in ip_host_addr:
                            try:
                                # 格式为"A.B.C.D hh:hh:hh:hh:hh:hh"
                                parts = ip_host_addr.split(' ')
                                if len(parts) >= 2:
                                    mac_addr = parts[1]
                                    # 验证MAC地址格式
                                    if len(mac_addr.replace(':', '')) == 12:
                                        record['mac'] = mac_addr
                                        if MULTI_LOGGER_AVAILABLE:
                                            acct_info(f"H3C MAC address extracted from Ip-Host-Addr: {mac_addr}")
                                        else:
                                            logger.info(f"H3C MAC address extracted from Ip-Host-Addr: {mac_addr}")
                            except Exception as e:
                                logger.warning(f"Error extracting MAC from H3C Ip-Host-Addr: {e}")

                        # 安全地处理IPv6流量统计
                        try:
                            h3c_ipv6_input_octets = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Input-Octets', 0))
                            h3c_ipv6_input_gigawords = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Input-Gigawords', 0))
                            h3c_ipv6_output_octets = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Output-Octets', 0))
                            h3c_ipv6_output_gigawords = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Output-Gigawords', 0))

                            record['user_ipv6_inoctets'] = h3c_ipv6_input_octets + (4 * h3c_ipv6_input_gigawords * 1024 * 1024 * 1024)
                            record['user_ipv6_outoctets'] = h3c_ipv6_output_octets + (4 * h3c_ipv6_output_gigawords * 1024 * 1024 * 1024)
                            record['user_ipv6_inpackets'] = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Input-Packets', 0))
                            record['user_ipv6_outpackets'] = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Output-Packets', 0))
                            acct_update_flag = int(safe_get_packet_attr(pkt, 'Acct-Update-Address', 0))
                            # 将更新标志存储到record中以便后续使用
                            record['H3C-Acct-Update-Address'] = acct_update_flag
                        except (ValueError, TypeError):
                            logger.debug("Invalid H3C IPv6 statistics values")
                            record['user_ipv6_inoctets'] = 0
                            record['user_ipv6_outoctets'] = 0
                            record['user_ipv6_inpackets'] = 0
                            record['user_ipv6_outpackets'] = 0

                    elif bras.vendor == 'ZTE':
                        # 处理中兴设备的NAT和IPv6属性
                        zte_nat_addr = safe_get_packet_attr(pkt, 'ZTE-NAT-IP-Address')
                        if zte_nat_addr:
                            record['user_nat_framedip'] = zte_nat_addr

                        record['user_nat_beginport'] = safe_get_packet_attr(pkt, 'ZTE-NAT-start-Port', 0)
                        record['user_nat_endport'] = safe_get_packet_attr(pkt, 'ZTE-NAT-end-Port', 0)

                        # 安全地处理IPv6流量统计
                        try:
                            zte_v6_input_octets = int(safe_get_packet_attr(pkt, 'ZTE-v6-Input-Octets', 0))
                            zte_v6_input_giga = int(safe_get_packet_attr(pkt, 'ZTE-v6-Input-Giga-Octets', 0))
                            zte_v6_output_octets = int(safe_get_packet_attr(pkt, 'ZTE-v6-Output-Octets', 0))
                            zte_v6_output_giga = int(safe_get_packet_attr(pkt, 'ZTE-v6-Outut-Giga-Octets', 0))

                            record['user_ipv6_inoctets'] = zte_v6_input_octets * 4 * zte_v6_input_giga * 1024 * 1024 * 1024
                            record['user_ipv6_outoctets'] = zte_v6_output_octets * 4 * zte_v6_output_giga * 1024 * 1024 * 1024
                            record['user_ipv6_inpackets'] = int(safe_get_packet_attr(pkt, 'ZTE-v6-Input-Packets', 0))
                            record['user_ipv6_outpackets'] = int(safe_get_packet_attr(pkt, 'ZTE-v6-Output-Packets', 0))
                            acct_update_flag = int(safe_get_packet_attr(pkt, 'ZTE-Acct-Address-Change', 0))
                            # 将更新标志存储到record中以便后续使用
                            record['ZTE-Acct-Address-Change'] = acct_update_flag
                        except (ValueError, TypeError):
                            logger.debug("Invalid ZTE IPv6 statistics values")
                            record['user_ipv6_inoctets'] = 0
                            record['user_ipv6_outoctets'] = 0
                            record['user_ipv6_inpackets'] = 0
                            record['user_ipv6_outpackets'] = 0

                except (KeyError, AttributeError) as e:
                    logger.warning(f"Error accessing BRAS info for {bras_ip}: {e}")
                    record['bras_area'] = 0
            else:
                # BRAS信息不在hosts配置中，保持默认值
                if bras_ip:
                    logger.warning(f"BRAS IP {bras_ip} not found in hosts configuration")
                    # 保持record['bras_area'] = 0 的默认值

            # 构建更新记录
            if acct_update_flag:
                # 如果存在特定的属性，则更新onlinerecord的相应字段
                acct_update_record = {
                    'user_nat_framedip': record.get('user_nat_framedip', None),
                    'user_nat_beginport': record.get('user_nat_beginport', 0),
                    'user_nat_endport': record.get('user_nat_endport', 0),
                    'user_framedipv6': record.get('user_framedipv6', None),
                    'user_delegated_ipv6prefix': record.get('user_delegated_ipv6prefix', None),
                    'packet_type': record['packet_type'],
                    'packet_process_time': now,
                    'radius_server': self.server.hostname
                }
            else:
                # 否则，只更新特定的流量统计字段
                acct_update_record = {
                    'user_ipv4_outoctets': record['user_ipv4_outoctets'],
                    'user_ipv4_inoctets': record.get('user_ipv4_inoctets', 0),
                    'user_ipv4_outpackets': record.get('user_ipv4_outpackets', 0),
                    'user_ipv4_inpackets': record.get('user_ipv4_inpackets', 0),
                    'user_ipv6_outoctets': record.get('user_ipv6_outoctets', 0),
                    'user_ipv6_inoctets': record.get('user_ipv6_inoctets', 0),
                    'user_ipv6_outpackets': record.get('user_ipv6_outpackets', 0),
                    'user_ipv6_inpackets': record.get('user_ipv6_inpackets', 0),
                    'packet_type': record['packet_type'],
                    'packet_process_time': now,
                    'radius_server': self.server.hostname
                }

            return acct_update_record

        except Exception as e:
            logger.warning(f"Error processing BRAS attributes: {e}")
            return {}

    def _execute_accounting_operation(self, acct_type, record, acct_update_record, session_id):
        """执行计费操作"""
        try:
            import dbutil

            if acct_type == 'Start':
                # 插入在线记录
                try:
                    dbutil.insert_onlinerecord(record)
                    logger.info("Accounting Start: inserted into onlinerecord table")
                except Exception as e:
                    logger.error(f"Error inserting onlinerecord: {e}")

            elif acct_type == 'Alive':
                # 更新在线记录
                try:
                    if session_id:
                        dbutil.update_onlinerecord_by_sessionid(session_id, acct_update_record, record, 0)
                        logger.info("Accounting Alive: updated onlinerecord table")
                except Exception as e:
                    logger.error(f"Error updating onlinerecord: {e}")

            elif acct_type == 'Stop':
                # 插入详细记录并删除在线记录
                # down_reason已经在主函数中设置了
                try:
                    dbutil.insert_detail(record)
                    logger.info("Accounting Stop: inserted into detail table")
                except Exception as e:
                    logger.error(f"Error inserting detail record: {e}")

                if session_id:
                    try:
                        dbutil.delete_onlinerecord_by_sessionid(session_id)
                        logger.info("Accounting Stop: deleted from onlinerecord table")
                    except Exception as e:
                        logger.error(f"Error deleting onlinerecord: {e}")

        except Exception as e:
            logger.error(f"Error executing accounting operation: {e}")

    async def _execute_accounting_operation_async(self, acct_type, record, acct_update_record, session_id):
        """异步执行计费操作（性能优化版本）"""
        try:
            if acct_type == 'Start':
                # 插入在线记录
                try:
                    await async_dbutil.insert_onlinerecord(record)
                    if MULTI_LOGGER_AVAILABLE:
                        acct_info("Accounting Start: inserted into onlinerecord table")
                    else:
                        logger.info("Accounting Start: inserted into onlinerecord table")
                except Exception as e:
                    if MULTI_LOGGER_AVAILABLE:
                        acct_error(f"Error inserting onlinerecord: {e}")
                    else:
                        logger.error(f"Error inserting onlinerecord: {e}")

            elif acct_type == 'Alive':
                # 更新在线记录
                try:
                    if session_id:
                        # 获取acct_update_flag值
                        acct_update_flag = 0
                        if 'Huawei-Acct-Update-Address' in record:
                            acct_update_flag = record.get('Huawei-Acct-Update-Address', 0)
                        elif 'H3C-Acct-Update-Address' in record:
                            acct_update_flag = record.get('H3C-Acct-Update-Address', 0)
                        elif 'ZTE-Acct-Address-Change' in record:
                            acct_update_flag = record.get('ZTE-Acct-Address-Change', 0)

                        if MULTI_LOGGER_AVAILABLE:
                            acct_info(f"Updating onlinerecord with {len(acct_update_record)} fields, update_flag={acct_update_flag}")
                            for field, value in acct_update_record.items():
                                acct_info(f"  {field}: {value}")

                        await async_dbutil.update_onlinerecord_by_sessionid(session_id, acct_update_record, record, acct_update_flag)
                        if MULTI_LOGGER_AVAILABLE:
                            acct_info("Accounting Alive: updated onlinerecord table")
                        else:
                            logger.info("Accounting Alive: updated onlinerecord table")
                except Exception as e:
                    if MULTI_LOGGER_AVAILABLE:
                        acct_error(f"Error updating onlinerecord: {e}")
                    else:
                        logger.error(f"Error updating onlinerecord: {e}")

            elif acct_type == 'Stop':
                # 插入详细记录并删除在线记录
                try:
                    await async_dbutil.insert_detail(record)
                    if MULTI_LOGGER_AVAILABLE:
                        acct_info("Accounting Stop: inserted into detail table")
                    else:
                        logger.info("Accounting Stop: inserted into detail table")
                except Exception as e:
                    if MULTI_LOGGER_AVAILABLE:
                        acct_error(f"Error inserting detail record: {e}")
                    else:
                        logger.error(f"Error inserting detail record: {e}")

                if session_id:
                    try:
                        await async_dbutil.delete_onlinerecord_by_sessionid(session_id)
                        if MULTI_LOGGER_AVAILABLE:
                            acct_info("Accounting Stop: deleted from onlinerecord table")
                        else:
                            logger.info("Accounting Stop: deleted from onlinerecord table")
                    except Exception as e:
                        if MULTI_LOGGER_AVAILABLE:
                            acct_error(f"Error deleting onlinerecord: {e}")
                        else:
                            logger.error(f"Error deleting onlinerecord: {e}")

        except Exception as e:
            if MULTI_LOGGER_AVAILABLE:
                acct_error(f"Error executing async accounting operation: {e}")
            else:
                logger.error(f"Error executing async accounting operation: {e}")

    async def _record_coa_async(self, pkt, reply, addr):
        """异步记录CoA操作"""
        try:
            from datetime import datetime
            import pytz

            # 构建CoA记录
            now = datetime.now(pytz.timezone('Asia/Shanghai'))

            # 确定CoA类型
            if pkt.code == packet.CoARequest:
                coa_type = 'CoA-Request'
            elif pkt.code == packet.DisconnectRequest:
                coa_type = 'Disconnect-Request'
            else:
                coa_type = f'Unknown-{pkt.code}'

            # 确定结果
            if reply.code in [packet.CoAACK, packet.DisconnectACK]:
                coa_result = 'Success'
            else:
                coa_result = 'Failed'

            # 收集请求属性
            request_attributes = []
            for attr in pkt.keys():
                try:
                    value = pkt[attr]
                    if isinstance(value, list) and len(value) == 1:
                        value = value[0]
                    request_attributes.append(f"{attr}={value}")
                except Exception:
                    pass

            record = {
                'session_id': safe_get_packet_attr(pkt, 'Acct-Session-Id', ''),
                'user_name': safe_get_packet_attr(pkt, 'User-Name', ''),
                'user_domain': safe_get_packet_attr(pkt, 'User-Domain', ''),
                'bras_ip': safe_get_packet_attr(pkt, 'NAS-IP-Address', addr[0]),
                'coa_type': coa_type,
                'coa_result': coa_result,
                'coa_time': now,
                'radius_server': self.server.hostname,
                'client_ip': addr[0],
                'request_attributes': '; '.join(request_attributes)
            }

            # 异步插入数据库
            await async_dbutil.insert_coa_record(record)

        except Exception as e:
            if MULTI_LOGGER_AVAILABLE:
                coa_error(f"Error recording CoA operation: {e}")
            else:
                logger.error(f"Error recording CoA operation: {e}")


async def main():
    """主函数"""
    global srv_instance

    # 初始化数据库连接池
    await async_dbutil.init_db_pool()

    # 初始化VPDN域名列表
    try:
        vpdn_list = await async_dbutil.get_vpdn_list()
        if vpdn_list:
            logger.debug(f"VPDN list type: {type(vpdn_list)}, content: {vpdn_list}")
            # 安全地处理VPDN域名列表
            for vpdn in vpdn_list:
                if isinstance(vpdn, dict) and 'vpdn_domain' in vpdn:
                    vpdn_domains.add(vpdn['vpdn_domain'])
                else:
                    logger.warning(f"Invalid VPDN entry: {vpdn}")
            logger.info(f"Loaded {len(vpdn_domains)} VPDN domains")
    except Exception as e:
        logger.error(f"Failed to load VPDN domains: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

    # 创建服务器实例
    srv_instance = AsyncRadiusServer()

    # 初始化BRAS列表
    try:
        bras_list = await async_dbutil.get_bras_list()
        if bras_list:
            logger.info(f"Retrieved {len(bras_list)} BRAS devices from database")
            for bras in bras_list:
                logger.info(f"BRAS: {bras['bras_ip']}, vendor: {bras['bras_vendor']}, area: {bras['bras_area']}")

            srv_instance.hosts = {
                bras['bras_ip']: server.RemoteHost(
                    bras['bras_ip'],
                    bras['bras_secret'].encode(),
                    bras['bras_ip'],
                    bras['bras_model'],
                    bras['bras_vendor'],
                    bras['bras_area']
                ) for bras in bras_list
            }
            logger.info(f"Loaded {len(bras_list)} BRAS devices into hosts dictionary")

            # 验证hosts字典中的数据
            # for ip, host in srv_instance.hosts.items():
            #     logger.info(f"Host {ip}: vendor={host.vendor}, area={host.area}")
        else:
            logger.warning("No BRAS devices found in database")
    except Exception as e:
        logger.error(f"Failed to load BRAS devices: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

    # 启动服务器
    logger.info("Starting async RADIUS server...")
    await srv_instance.start_udp_servers()


if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
