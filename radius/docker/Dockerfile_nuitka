# Nuitka编译版本的Dockerfile - 优化版
FROM python:3.12-slim AS builder

# 接收代理参数
ARG http_proxy
ARG https_proxy
ARG no_proxy

# 设置代理环境变量
ENV http_proxy=${http_proxy}
ENV https_proxy=${https_proxy}
ENV no_proxy=${no_proxy}

# 配置apt代理
RUN if [ -n "$http_proxy" ]; then \
        echo "Acquire::http::Proxy \"$http_proxy\";" > /etc/apt/apt.conf.d/01proxy; \
        echo "Acquire::https::Proxy \"$https_proxy\";" >> /etc/apt/apt.conf.d/01proxy; \
        echo "Acquire::ftp::Proxy \"$http_proxy\";" >> /etc/apt/apt.conf.d/01proxy; \
        echo "=== APT代理配置 ===" && \
        cat /etc/apt/apt.conf.d/01proxy; \
    else \
        echo "=== 未设置代理，使用清华源 ==="; \
    fi

# 配置源（清理冲突并设置正确的源）
RUN echo "=== 清理现有源配置 ===" && \
    rm -f /etc/apt/sources.list.d/* && \
    if [ -n "$http_proxy" ]; then \
        echo "=== 使用代理访问官方源 ===" && \
        echo "# 官方源配置（包含contrib和non-free）" > /etc/apt/sources.list && \
        echo "deb http://deb.debian.org/debian bookworm main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb http://deb.debian.org/debian bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb http://deb.debian.org/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
    else \
        echo "=== 配置清华源 ===" && \
        echo "# 清华大学源配置" > /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
    fi

# 验证源配置并安装编译依赖
RUN echo "=== 验证APT源配置 ===" && \
    echo "sources.list内容:" && cat /etc/apt/sources.list && \
    echo "=== 开始更新包列表 ===" && \
    apt-get update && \
    echo "=== 安装编译依赖 ===" && \
    apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    make \
    ccache \
    patchelf \
    chrpath \
    && rm -rf /var/lib/apt/lists/* \
    && echo "=== 编译依赖安装完成 ==="

# 配置pip源
RUN if [ -n "$http_proxy" ]; then \
        echo "=== 使用代理访问官方PyPI ===" && \
        pip config set global.index-url https://pypi.org/simple && \
        pip config set global.proxy $http_proxy; \
    else \
        echo "=== 配置pip清华源 ===" && \
        pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
        pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn; \
    fi && \
    echo "=== pip配置完成 ===" && \
    pip config list

# 设置工作目录
WORKDIR /build

# 只复制radius相关的必要文件
COPY requirements_async.txt /build/
COPY start_async_server.py /build/
COPY aaaserver/ /build/aaaserver/
COPY pyrad/ /build/pyrad/
COPY conf/ /build/conf/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_async.txt

# 安装Nuitka和相关工具
RUN pip install nuitka ordered-set

# Nuitka编译 - 保守优化版本（确保功能正常）
RUN echo "=== 开始Nuitka编译 ===" && \
    python -m nuitka \
    --standalone \
    --onefile \    
    --assume-yes-for-downloads \
    --output-filename=radius_server \
    --include-data-dir=conf=conf \
    --include-package=aaaserver \
    --include-package=pyrad \
    --include-package=aiomysql \
    --include-package=mysql.connector \
    --include-package=asyncio \
    --include-package=logging \
    --include-package=configparser \
    --include-package=ipaddress \
    --include-package=pytz \
    --include-package=netaddr \
    --include-package=six \
    --include-package=weakref \
    --include-package=threading \
    --include-package=concurrent.futures \
    --enable-plugin=anti-bloat \
    --remove-output \
    --no-pyi-file \
    --lto=no \
    --jobs=$(nproc) \
    --nofollow-import-to=pytest,unittest,test,tests \
    --nofollow-import-to=setuptools,distutils,pip \
    start_async_server.py && \
    echo "=== Nuitka编译完成 ==="

# 验证编译结果
RUN echo "=== 验证编译结果 ===" && \
    ls -la && \
    if [ -f "radius_server" ]; then \
        echo "=== 找到编译的可执行文件 ===" && \
        ls -lh radius_server && \
        test -x radius_server && \
        echo "=== 编译验证完成 ==="; \
    else \
        echo "=== 错误：未找到编译结果 ===" && \
        exit 1; \
    fi

# 运行时镜像 - 使用Ubuntu 24.04轻量镜像
FROM ubuntu:24.04

# 设置环境变量支持UTF-8
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

# 安装运行时必要的依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    locales \
    && locale-gen en_US.UTF-8 \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户（使用主机用户ID 1000）
RUN groupadd -r -g  radius && useradd -r -u  -g radius radius

# 创建应用目录和logs目录
RUN mkdir -p /app/logs && chown radius:radius /app && chown radius:radius /app/logs

# 复制编译后的可执行文件和配置文件
COPY --from=builder /build/radius_server /app/radius_server
COPY --from=builder /build/conf/ /app/conf/

# 设置文件权限（在切换用户之前）
RUN chmod 755 /app/radius_server && \
    chmod -R 755 /app/conf && \
    chown -R radius:radius /app && \
    ls -la /app/ && \
    echo "=== 权限设置完成 ===" && \
    echo "=== 测试可执行文件 ===" && \
    file /app/radius_server && \
    ldd /app/radius_server && \
    echo "=== 测试完成 ==="

# 切换到非root用户
USER radius

# 设置工作目录
WORKDIR /app

# 暴露端口
EXPOSE 1812/udp 1813/udp 3799/udp

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ["/app/radius_server", "--health-check"] || exit 1

# 启动命令
ENTRYPOINT ["/app/radius_server"]
