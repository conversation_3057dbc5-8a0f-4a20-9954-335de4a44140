[loggers]
keys=root,system,authentication,accounting,coa

[handlers]
keys=console<PERSON><PERSON><PERSON>,system<PERSON><PERSON><PERSON>,auth<PERSON><PERSON><PERSON>,acct<PERSON><PERSON><PERSON>,coa<PERSON><PERSON>ler

[formatters]
keys=simpleFormatter,contextFormatter

[logger_root]
level=DEBUG
handlers=consoleHandler

[logger_system]
level=INFO
handlers=systemHandler
qualname=system
propagate=0

[logger_authentication]
level=INFO
handlers=authHandler
qualname=authentication
propagate=0

[logger_accounting]
level=DEBUG
handlers=acctHandler
qualname=accounting
propagate=0

[logger_coa]
level=INFO
handlers=coaHandler
qualname=coa
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=simpleFormatter
args=(sys.stdout,)

[handler_systemHandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=simpleFormatter
args=('logs/system.log', 'midnight', 1, 30)

[handler_authHandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=contextFormatter
args=('logs/authentication.log', 'H', 1, 168)

[handler_acctHandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=contextFormatter
args=('logs/accounting.log', 'H', 1, 168)

[handler_coaHandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=contextFormatter
args=('logs/coa.log', 'H', 1, 168)

[formatter_simpleFormatter]
format=[%(asctime)s] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_contextFormatter]
format=[%(asctime)s.%(msecs)03d] [%(levelname)s] [%(request_id)s][%(user_name)s][%(client_ip)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S