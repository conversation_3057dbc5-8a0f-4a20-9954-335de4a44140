[authentication]
# Authentication switch configuration
# 1 = enable authentication, 0 = disable authentication (log only)

# Username verification switch
username_check_enabled = 1

# Password verification switch
password_check_enabled = 0

# User status verification switch
user_status_check_enabled = 1

# Online limit verification switch
online_limit_check_enabled = 0

# Domain existence verification switch
domain_check_enabled = 1

[authorization]
# Authorization configuration

# Session-Timeout (seconds) - session timeout
session_timeout = 172800

# Acct-Interim-Interval (seconds) - accounting interim interval
acct_interim_interval = 7200

# Framed-IP-Netmask - framed IP netmask
framed_ip_netmask = ***************

# Service-Type - service type (2=Framed)
service_type = 2

# Framed-Protocol - framed protocol (1=PPP)
framed_protocol = 1

[performance]
# Performance configuration

# Maximum concurrent coroutines
max_concurrent_tasks = 500

# Single task timeout (seconds)
task_timeout_seconds = 30.0

# Statistics report interval (seconds)
stats_interval = 60

[coa]
# CoA (Change of Authorization) server configuration

# CoA server enable switch
coa_enabled = 1

# CoA server listening port
coa_port = 3799

# CoA request processing timeout (seconds)
coa_timeout_seconds = 10.0

# CoA operation type configuration
# Supported operations: disconnect, coa_request
supported_operations = disconnect,coa_request

# CoA response configuration
# Default response codes: CoA-ACK(44), CoA-NAK(45), Disconnect-ACK(40), Disconnect-NAK(41)
default_coa_response = nak
default_disconnect_response = nak
