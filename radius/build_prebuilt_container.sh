#!/bin/bash

# Build script for pre-compiled radius server container
# This script builds a Docker image using the pre-compiled radius deployment directory

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the radius directory
if [ ! -f "start_async_server.py" ]; then
    print_error "Please run this script from the radius directory"
    exit 1
fi

# Find the latest radius_deploy_* directory
DEPLOY_DIR=$(ls -1d radius_deploy_* 2>/dev/null | sort -r | head -n1)

if [ -z "$DEPLOY_DIR" ]; then
    print_error "No radius_deploy_* directory found. Please compile the radius server first."
    exit 1
fi

print_info "Found deployment directory: $DEPLOY_DIR"

# Check if required files exist in the deployment directory
REQUIRED_FILES=("radius_server" "start.sh" "health_check.sh" "conf")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -e "$DEPLOY_DIR/$file" ]; then
        print_error "Required file/directory '$file' not found in $DEPLOY_DIR"
        exit 1
    fi
done

# Set image name and tag
IMAGE_NAME="smartaaa-radius-prebuilt"
IMAGE_TAG="latest"

print_info "Building Docker image: $IMAGE_NAME:$IMAGE_TAG"
print_info "Using deployment directory: $DEPLOY_DIR"

# Build the Docker image
docker build \
    -f docker/Dockerfile_prebuilt \
    -t "$IMAGE_NAME:$IMAGE_TAG" \
    .

if [ $? -eq 0 ]; then
    print_info "Docker image built successfully: $IMAGE_NAME:$IMAGE_TAG"
    print_info "You can now run the container with:"
    echo "  docker run -d --name radius-server -p 1812:1812/udp -p 1813:1813/udp -p 3799:3799/udp $IMAGE_NAME:$IMAGE_TAG"
else
    print_error "Failed to build Docker image"
    exit 1
fi
